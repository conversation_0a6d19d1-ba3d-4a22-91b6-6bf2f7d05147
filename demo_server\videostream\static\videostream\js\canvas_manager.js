class CanvasManager {
    constructor(canvasElement) {
      this.canvas = canvasElement;
      this.ctx = this.canvas.getContext('2d');
      this.polygons = [];
      this.currentPolygon = null;
    }
  
    setPolygons(polygons, currentPolygon = null) {
      this.polygons = polygons;
      this.currentPolygon = currentPolygon;
    }
  
    clear() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
  
    draw() {
      this.clear();
      for (const poly of this.polygons) {
        poly.draw(this.ctx);
      }
      if (this.currentPolygon) {
        this.currentPolygon.draw(this.ctx);
      }
    }
  
    resizeToMatch(videoElement) {
      // Get the computed style of the video element
      const videoStyles = window.getComputedStyle(videoElement);
      const container = videoElement.parentElement;
      const containerRect = container.getBoundingClientRect();
      
      // Get the actual displayed size and position of the video
      const videoRect = videoElement.getBoundingClientRect();
      
      // Set canvas dimensions to match the video's actual dimensions
      this.canvas.width = videoElement.clientWidth;
      this.canvas.height = videoElement.clientHeight;
      
      // Position the canvas to precisely match the video position within container
      this.canvas.style.position = 'absolute';
      this.canvas.style.left = `${videoRect.left - containerRect.left}px`;
      this.canvas.style.top = `${videoRect.top - containerRect.top}px`;
      
      // Log for debugging
      console.log('Video dimensions:', videoElement.clientWidth, videoElement.clientHeight);
      console.log('Canvas dimensions:', this.canvas.width, this.canvas.height);
      console.log('Canvas position:', this.canvas.style.left, this.canvas.style.top);
    }
  }
  
window.CanvasManager = CanvasManager;
  