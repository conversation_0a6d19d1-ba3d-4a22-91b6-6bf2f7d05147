{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2><i class="bi bi-pencil"></i> Kamera <PERSON>ü<PERSON>le</h2>
            <p class="text-muted">Kamera bilgilerini güncelleyin</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'cameras:index' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kamera Listesi
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Kamera Bilgileri</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.venue.id_for_label }}" class="form-label">
                                {{ form.venue.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.venue }}
                            {% if form.venue.help_text %}
                                <div class="form-text">{{ form.venue.help_text }}</div>
                            {% endif %}
                            {% if form.venue.errors %}
                                <div class="text-danger">{{ form.venue.errors }}</div>
                            {% endif %}
                            {% if not form.venue.queryset %}
                                <div class="alert alert-warning mt-2">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    Henüz hiç mekan oluşturmamışsınız. 
                                    <a href="{% url 'alerts:venue_create' %}" class="alert-link">Önce bir mekan oluşturun</a>
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.camera_type.id_for_label }}" class="form-label">
                                {{ form.camera_type.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.camera_type }}
                            {% if form.camera_type.help_text %}
                                <div class="form-text">{{ form.camera_type.help_text }}</div>
                            {% endif %}
                            {% if form.camera_type.errors %}
                                <div class="text-danger">{{ form.camera_type.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.rtsp_url.id_for_label }}" class="form-label">
                                {{ form.rtsp_url.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.rtsp_url }}
                            {% if form.rtsp_url.help_text %}
                                <div class="form-text">{{ form.rtsp_url.help_text }}</div>
                            {% endif %}
                            {% if form.rtsp_url.errors %}
                                <div class="text-danger">{{ form.rtsp_url.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> Değişiklikleri Kaydet
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="{% url 'cameras:index' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

