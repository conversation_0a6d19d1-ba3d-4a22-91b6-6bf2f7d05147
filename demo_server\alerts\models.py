import numpy as np
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings
import os
from django.utils.crypto import get_random_string


class Venue(models.Model):
    """Venue (Mekan) modeli - kameralar bir mekana ait olur ve entry/exit o mekan için takip edilir"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='venues')
    name = models.CharField(max_length=100, help_text='Mekan adı (örn: <PERSON>, 1. Kat, Toplantı Salonu)')
    description = models.TextField(blank=True, help_text='Mekan açıklaması')
    is_active = models.BooleanField(default=True, help_text='Mekan aktif mi?')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['user', 'is_active']),
        ]

    def __str__(self):
        return self.name

    def get_entry_cameras(self):
        """Bu mekana ait entry kameralarını döndür"""
        return self.cameras.filter(camera_type='ENTRY')

    def get_exit_cameras(self):
        """Bu mekana ait exit kameralarını döndür"""
        return self.cameras.filter(camera_type='EXIT')


def unique_file_path(instance, filename):
    ext = filename.split('.')[-1]
    new_filename = f"{get_random_string(20)}.{ext}"

    # Çoklu kişi durumunda özel path
    if isinstance(instance, AlertPhoto) and instance.is_multiple_face:
        return os.path.join('alert_photos', 'multiple_faces', filename)

    # AlertPhoto için
    elif isinstance(instance, AlertPhoto):
        if instance.person.is_unknown:
            return os.path.join('alert_photos', 'unknowns', instance.person.name, new_filename)
        return os.path.join('alert_photos', instance.person.name, new_filename)

    # Alarm için
    elif isinstance(instance, Alarm):
        alarm_type_folder = instance.alarm_type.lower() + '_alarms'
        if instance.person.is_unknown:
            return os.path.join('alert_photos', 'unknowns', instance.person.name, alarm_type_folder, new_filename)
        return os.path.join('alert_photos', instance.person.name, alarm_type_folder, new_filename)

class AlertPerson(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='alert_persons', null=True, blank=True)
    name = models.CharField(max_length=100)
    employee_id = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        help_text='Employee ID from company system for known persons'
    )
    is_unknown = models.BooleanField(default=False)
    first_seen_camera = models.ForeignKey('cameras.Camera', on_delete=models.SET_NULL, null=True)
    last_seen_date = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['name', 'is_unknown']),
            models.Index(fields=['employee_id']),
        ]

    def __str__(self):
        if self.is_unknown:
            return f"{self.name} (Unknown)"
        elif self.employee_id:
            return f"{self.name} (ID: {self.employee_id})"
        else:
            return f"{self.name} (Known)"


class PersonVenueStatus(models.Model):
    """
    Tracks whether a person is currently inside or outside a specific venue.
    This helps prevent duplicate ENTRY/EXIT alarms and provides real-time status.
    """
    PERSON_STATUS_CHOICES = [
        ('OUTSIDE', 'Outside'),
        ('INSIDE', 'Inside'),
    ]
    
    person = models.ForeignKey(AlertPerson, on_delete=models.CASCADE, related_name='venue_statuses')
    venue = models.ForeignKey(Venue, on_delete=models.CASCADE, related_name='person_statuses')
    status = models.CharField(
        max_length=10,
        choices=PERSON_STATUS_CHOICES,
        default='OUTSIDE',
        help_text='Current status of person in this venue'
    )
    last_updated = models.DateTimeField(auto_now=True)
    last_camera = models.ForeignKey('cameras.Camera', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['person', 'venue']
        indexes = [
            models.Index(fields=['person', 'venue', 'status']),
            models.Index(fields=['venue', 'status']),
        ]

    def __str__(self):
        return f"{self.person.name} in {self.venue.name}: {self.status}"

    @classmethod
    def get_or_create_status(cls, person, venue):
        """Get or create a person's venue status, defaulting to OUTSIDE"""
        status, created = cls.objects.get_or_create(
            person=person,
            venue=venue,
            defaults={'status': 'OUTSIDE'}
        )
        return status, created

    def update_status(self, new_status, camera=None):
        """Update person's status in this venue"""
        self.status = new_status
        if camera:
            self.last_camera = camera
        self.save(update_fields=['status', 'last_camera', 'last_updated'])

class AlertPhoto(models.Model):
    person = models.ForeignKey(AlertPerson, related_name='photos', on_delete=models.CASCADE, null=True, blank=True)
    photo = models.ImageField(upload_to=unique_file_path)
    image_vector_facenet = models.BinaryField(null=True, blank=True)  # Binary field for Facenet
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    is_multiple_face = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=['person', 'is_primary']),
        ]

    def save(self, *args, **kwargs):
        if self.is_primary:
            # Ensure only one primary photo per person
            AlertPhoto.objects.filter(person=self.person, is_primary=True).update(is_primary=False)

        if self.image_vector_facenet is not None and not isinstance(self.image_vector_facenet, (bytes, memoryview)):
            try:
                self.image_vector_facenet = np.array(self.image_vector_facenet, dtype=np.float32).tobytes()
            except Exception as e:
                print(f"Error converting vector to bytes: {e}")
                self.image_vector_facenet = None

        super().save(*args, **kwargs)

    def get_facenet_vector(self):
        """Convert stored bytes back to numpy array"""
        if self.image_vector_facenet:
            try:
                return np.frombuffer(self.image_vector_facenet, dtype=np.float32)
            except:
                return None
        return None

class Alarm(models.Model):
    ALARM_TYPE_CHOICES = [
        ('ENTRY', 'Entry Alarm'),
        ('EXIT', 'Exit Alarm'),
    ]
    
    alert_photo = models.ForeignKey(AlertPhoto, on_delete=models.CASCADE, null=True, blank=True)
    person = models.ForeignKey(AlertPerson, related_name='alarms', on_delete=models.CASCADE)    
    camera = models.ForeignKey('cameras.Camera', on_delete=models.CASCADE)
    venue = models.ForeignKey(Venue, on_delete=models.SET_NULL, null=True, blank=True, 
                             help_text='Venue at the time of alarm (snapshot of camera venue)')
    alarm_type = models.CharField(
        max_length=15,
        choices=ALARM_TYPE_CHOICES,
        help_text='Type of alarm - entry or exit only'
    )
    date = models.DateTimeField(auto_now_add=True)
    video_snapshot = models.FileField(upload_to=unique_file_path, null=True, blank=True)
    confidence = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        default=0.0
    )
    is_auto_generated = models.BooleanField(
        default=False,
        help_text='True if this alarm was automatically generated (missed exit, etc.)'
    )
    auto_generation_reason = models.CharField(
        max_length=50, 
        blank=True, 
        null=True,
        help_text='Reason for auto generation: MISSED_EXIT_REENTRY, etc.'
    )
    @property
    def confidence_percentage(self):
        return f"%{self.confidence * 100:.1f}"
    
    class Meta:
        ordering = ['-date']
        indexes = [
            models.Index(fields=['person', '-date']),
        ]

    def __str__(self):
        alarm_type_display = self.get_alarm_type_display()
        return f"{alarm_type_display}: {self.person.name} at {self.date:%Y-%m-%d %H:%M}"