# Generated by Django 5.1.4 on 2025-09-18 18:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0013_venue_personvenuestatus_and_more'),
        ('cameras', '0006_camera_camera_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='camera',
            name='venue',
            field=models.ForeignKey(blank=True, help_text='Bu kameranın ait olduğu mekan', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cameras', to='alerts.venue'),
        ),
        migrations.AlterField(
            model_name='camera',
            name='camera_type',
            field=models.CharField(choices=[('ENTRY', 'Entry Camera'), ('EXIT', 'Exit Camera')], default='ENTRY', help_text='Camera type for visit tracking', max_length=10),
        ),
    ]
