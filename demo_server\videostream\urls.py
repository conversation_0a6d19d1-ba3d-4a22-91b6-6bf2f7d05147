from django.urls import path
from . import views
from .views import get_zones

app_name = 'videostream'

urlpatterns = [
    path('viewer/', views.stream_viewer, name='stream_viewer'),
    path('start/', views.start_stream, name='start_stream'),
    path('stop/', views.stop_stream, name='stop_stream'),
    path('stream/', views.mjpeg_stream, name='mjpeg_stream'),
    path('zone_creator/', views.zone_creator, name='zone_creator'),
    path('save_zones/', views.zone_request_handler, name='save_zones'),
    path('get_zones/', views.get_zones, name='get_zones'),
    path('toggle_zone_filter/', views.toggle_zone_filter, name='toggle_zone_filter'),
    path('update_fps/', views.update_fps, name='update_fps'),
]