# Generated by Django 5.1.4 on 2025-09-23 14:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0018_remove_venue_user_remove_alarm_venue_and_more'),
        ('cameras', '0009_remove_camera_venue_alter_camera_camera_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Venue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='<PERSON>kan adı (örn: Ana <PERSON>, 1. Kat, Toplantı Salonu)', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Mekan açıklaması')),
                ('is_active', models.BooleanField(default=True, help_text='Mekan aktif mi?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venues', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PersonVenueStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('OUTSIDE', 'Outside'), ('INSIDE', 'Inside')], default='OUTSIDE', help_text='Current status of person in this venue', max_length=10)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_camera', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cameras.camera')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venue_statuses', to='alerts.alertperson')),
                ('venue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='person_statuses', to='alerts.venue')),
            ],
        ),
        migrations.AddField(
            model_name='alarm',
            name='venue',
            field=models.ForeignKey(blank=True, help_text='Venue at the time of alarm (snapshot of camera venue)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='alerts.venue'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['user', 'is_active'], name='alerts_venu_user_id_f8a3b9_idx'),
        ),
        migrations.AddIndex(
            model_name='personvenuestatus',
            index=models.Index(fields=['person', 'venue', 'status'], name='alerts_pers_person__efced0_idx'),
        ),
        migrations.AddIndex(
            model_name='personvenuestatus',
            index=models.Index(fields=['venue', 'status'], name='alerts_pers_venue_i_e8cfd5_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='personvenuestatus',
            unique_together={('person', 'venue')},
        ),
    ]
