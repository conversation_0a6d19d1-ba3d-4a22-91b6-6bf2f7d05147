{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <h2><i class="bi bi-plus-lg"></i> <PERSON><PERSON></h2>
            <p class="text-muted">Kameralarınızın atanacağı yeni bir mekan oluşturun</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{% url 'alerts:venue_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Mekan Listesi
            </a>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Mekan Bilgileri</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                Mekan Adı <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   required 
                                   maxlength="100"
                                   placeholder="örn: Ana Ofis, 1. Kat, Toplantı Salonu">
                            <div class="form-text">Mekanınızı tanımlayıcı bir isim verin</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Açıklama</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Mekan hakkında detayları yazabilirsiniz (opsiyonel)"></textarea>
                            <div class="form-text">Mekanın özelliklerini, konumunu veya diğer detayları açıklayabilirsiniz</div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       checked>
                                <label class="form-check-label" for="is_active">
                                    <i class="bi bi-check-circle text-success"></i> Mekan aktif
                                </label>
                                <div class="form-text">Pasif mekanlar için kamera ataması yapılamaz</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> Mekanı Oluştur
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="{% url 'alerts:venue_list' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bilgi Kartı -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-1">
                            <i class="bi bi-info-circle text-info display-6"></i>
                        </div>
                        <div class="col-md-11">
                            <h6>Mekan sistemi nasıl çalışır?</h6>
                            <ul class="mb-0">
                                <li>Her mekan için ayrı entry/exit takibi yapılır</li>
                                <li>Kameralar bir mekana atanarak ENTRY veya EXIT olarak tanımlanır</li>
                                <li>Kişiler farklı mekanlarda bağımsız olarak takip edilir</li>
                                <li>Times sayfasında mekan bazında gruplandırılmış raporlar görürsünüz</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}