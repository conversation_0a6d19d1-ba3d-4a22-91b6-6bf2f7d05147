import time
import stat
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.utils import timezone
from recognition.facenet_recognizer import FaceNetRecognizer
from .models import AlertPhoto, AlertPerson, Alarm, Venue
from .forms import AlertPhotoForm, AlertPersonForm, RenameUnknownPersonForm, AlertFilterForm
from .services import employee_photo_service
from .utils import calculate_image_vector, detect_faces
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .serializers import AlertPersonSerializer, AlertPhotoSerializer, AlarmSerializer
import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
from django.contrib import messages
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
import os
import shutil
import torch
from django.conf import settings
import logging
from datetime import datetime, date
from django.db.models import Q


logger = logging.getLogger(__name__)

@login_required
def index(request):
    total_persons = AlertPerson.objects.filter(Q(user=request.user) | Q(user__isnull=True)).count()
    return render(request, 'alerts/index.html', {'total_persons': total_persons})

@login_required
def delete_multiple_alerts(request):
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'delete_all':
            # Tüm alarmları sil
            Alarm.objects.filter(person__user=request.user).delete()
            messages.success(request, 'All alerts deleted successfully.')

        elif action == 'delete_selected':
            selected_alerts = request.POST.getlist('selected_alerts')
            if selected_alerts:
                # Seçili alarmları sil
                Alarm.objects.filter(
                    id__in=selected_alerts,
                    person__user=request.user
                ).delete()
                messages.success(request, 'Selected alerts deleted successfully.')
            else:
                messages.warning(request, 'No alerts were selected.')

    return redirect('alerts:alert_list')

@login_required
def add_alert_photo(request):
    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            person_id = form.cleaned_data['person']
            person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes = detect_faces(np.array(image))
                if not face_boxes:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_box = face_boxes[0]
                    face_image = image.crop(face_box)
                    if face_image.size[0] < 100 or face_image.size[1] < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        face_image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet
                        alert_photo.save()
                        messages.success(request, f'Photo {photo.name} added successfully')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()
    return render(request, 'alerts/add_photo.html', {'form': form})

@login_required
def add_alert_person(request):
    if request.method == 'POST':
        form = AlertPersonForm(request.POST)
        if form.is_valid():
            alert_person = form.save(commit=False)
            alert_person.user = request.user  # Set the user to the currently logged-in user
            alert_person.save()
            messages.success(request, 'Alert person added successfully.')
            return redirect('alerts:index')  # Redirect to the index page
    else:
        form = AlertPersonForm()
    return render(request, 'alerts/add_person.html', {'form': form})


@login_required
def person_detail(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id)
    # Check user permission for user's persons or allow API synced persons
    if person.user and person.user != request.user:
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied("You don't have permission to access this person.")
    
    # Her iki modeli de başlat
    facenet_recognizer = FaceNetRecognizer()

    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes, faces = detect_faces(np.array(image))

                if len(face_boxes) == 0:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_boxes = face_boxes[:, :-1]
                    face_boxes = face_boxes.astype(int)
                    face_boxes = face_boxes + [-1, -1, 1, 1]
                    face_box = face_boxes[0]
                    face_width = face_box[2] - face_box[0]
                    face_height = face_box[3] - face_box[1]
                    if face_width < 100 or face_height < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                        form = AlertPhotoForm()
                        return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet ki path'i olsun
                        alert_photo.save()

                        # Her iki modelin facebankına ekle
                        facenet_success = facenet_recognizer.add_face(person.name, alert_photo.photo.path)
                        
                        # Her iki model için embeddingler oluştur ve kaydet
                        try:
                            # FaceNet embeddingi hesapla
                            from alerts.utils import calculate_image_vector
                            facenet_vector = calculate_image_vector(alert_photo.photo.path, 'facenet')
                            if facenet_vector is not None:
                                alert_photo.image_vector_facenet = np.array(facenet_vector, dtype=np.float32).tobytes()
                                
                            # Güncellenmiş embedding'lerle kaydet
                            alert_photo.save()
                        except Exception as e:
                            logger.error(f"Error generating face embeddings: {str(e)}")

                        if facenet_success:
                            messages.success(request, f'Photo {photo.name} added successfully and added to facebank')
                        else:
                            messages.warning(request, f'Photo added to FaceNet facebank only: {photo.name}')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()

    return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})

@login_required
def person_list(request):
    # Base queryset - include user's persons and API synced persons (user=null)
    persons = AlertPerson.objects.filter(Q(user=request.user) | Q(user__isnull=True))
    
    # Search filters
    name_filter = request.GET.get('name', '').strip()
    employee_id_filter = request.GET.get('employee_id', '').strip()
    person_type_filter = request.GET.get('person_type', '')
    
    # Apply filters
    if name_filter:
        persons = persons.filter(name__icontains=name_filter)
    
    if employee_id_filter:
        persons = persons.filter(employee_id__icontains=employee_id_filter)
    
    if person_type_filter == 'known':
        persons = persons.filter(is_unknown=False)
    elif person_type_filter == 'unknown':
        persons = persons.filter(is_unknown=True)
    
    # Add counts
    persons = persons.annotate(
        photo_count=Count('photos', distinct=True),
        alarm_count=Count('alarms', distinct=True)
    ).order_by('name')
    
    total_persons = persons.count()
    
    context = {
        'persons': persons,
        'total_persons': total_persons,
    }
    
    return render(request, 'alerts/person_list.html', context)

@login_required
def person_times(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id)
    # Check user permission for user's persons or allow API synced persons
    if person.user and person.user != request.user:
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied("You don't have permission to access this person.")

    # Tarih parametresini al, yoksa bugünü kullan
    selected_date_str = request.GET.get('date')
    if selected_date_str:
        try:
            selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        except ValueError:
            selected_date = date.today()
    else:
        selected_date = date.today()

    # Seçilen tarihteki alarmları al (sadece ENTRY/EXIT)
    alarms = Alarm.objects.filter(
        person=person,
        date__date=selected_date,
        alarm_type__in=['ENTRY', 'EXIT']
    ).select_related('camera', 'venue').order_by('venue', 'date')

    # VENUE bazında verileri grupla
    venue_summary = {}
    has_data = False

    for alarm in alarms:
        # Use alarm.venue instead of camera.venue for historical accuracy
        venue_name = alarm.venue.name if alarm.venue else "Venue Atanmamış"
        venue_id = alarm.venue.id if alarm.venue else 'unassigned'
        
        if venue_id not in venue_summary:
            venue_summary[venue_id] = {
                'venue_name': venue_name,
                'venue_obj': alarm.venue,
                'entry_exit_pairs': [],
                'total_events': 0,
                'all_alarms': [],
                'cameras_used': set()
            }

        venue_summary[venue_id]['all_alarms'].append(alarm)
        venue_summary[venue_id]['total_events'] += 1
        venue_summary[venue_id]['cameras_used'].add(alarm.camera)
        has_data = True

    # Her venue için alarmları giriş-çıkış çiftlerine dönüştür
    for venue_id, data in venue_summary.items():
        all_alarms = data['all_alarms']
        pairs = []

        # Alarmları entry-exit mantığına göre çiftleştir
        # Entry ile başlayıp Exit ile devam eden çiftleri bul
        current_entry = None
        
        for alarm in all_alarms:
            if alarm.alarm_type == 'ENTRY':
                # Önceki entry varsa onu yalnız bırak
                if current_entry:
                    pairs.append({
                        'entry': current_entry,
                        'exit': None,
                        'entry_camera': current_entry.camera,
                        'exit_camera': None,
                        'duration': None,
                        'hours': None,
                        'minutes': None,
                    })
                current_entry = alarm
                
            elif alarm.alarm_type == 'EXIT' and current_entry:
                # Entry-Exit çifti buldu
                duration = alarm.date - current_entry.date
                total_seconds = int(duration.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                
                pairs.append({
                    'entry': current_entry,
                    'exit': alarm,
                    'entry_camera': current_entry.camera,
                    'exit_camera': alarm.camera,
                    'duration': duration,
                    'hours': hours,
                    'minutes': minutes,
                })
                current_entry = None

        # Son entry varsa onu da ekle
        if current_entry:
            pairs.append({
                'entry': current_entry,
                'exit': None,
                'entry_camera': current_entry.camera,
                'exit_camera': None,
                'duration': None,
                'hours': None,
                'minutes': None,
            })

        data['entry_exit_pairs'] = pairs

    # Get employee photo for known persons
    employee_photo = None
    if not person.is_unknown:
        try:
            # First try to use employeeId if available, otherwise fallback to name
            lookup_id = person.employee_id if person.employee_id else person.name
            employee_photo = employee_photo_service.get_employee_photo_by_id(lookup_id)
            if employee_photo:
                logger.info(f"Successfully retrieved employee photo for: {person.name} (ID: {lookup_id})")
            else:
                logger.info(f"No employee photo found for: {person.name} (ID: {lookup_id})")
        except Exception as e:
            logger.error(f"Error fetching employee photo for {person.name}: {str(e)}")
            employee_photo = None

    context = {
        'person': person,
        'selected_date': selected_date,
        'venue_summary': venue_summary,
        'has_data': has_data,
        'employee_photo': employee_photo,  # Add employee photo to context
    }

    return render(request, 'alerts/person_times.html', context)


def test_employee_api(request):
    """Test endpoint to check employee photo API functionality"""
    from django.http import JsonResponse
    
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        # Get cache stats
        stats = employee_photo_service.get_cache_stats()
        
        # Test API call if parameters provided
        employee_name = request.GET.get('name')
        employee_id = request.GET.get('id') 
        
        photo_result = None
        if employee_id:
            # Test by employee ID (preferred method)
            photo_data = employee_photo_service.get_employee_photo_by_id(employee_id)
            photo_result = {
                'lookup_type': 'employee_id',
                'lookup_value': employee_id,
                'photo_found': photo_data is not None,
                'photo_length': len(photo_data) if photo_data else 0,
            }
        elif employee_name:
            # Test by name (fallback method)
            photo_data = employee_photo_service.get_employee_photo_by_name(employee_name)
            photo_result = {
                'lookup_type': 'name',
                'lookup_value': employee_name,
                'photo_found': photo_data is not None,
                'photo_length': len(photo_data) if photo_data else 0,
            }
        
        return JsonResponse({
            'success': True,
            'cache_stats': stats,
            'photo_test': photo_result,
            'usage': {
                'by_employee_id': 'Add ?id=EMPLOYEE_ID to test specific employee photo fetch (recommended)',
                'by_name': 'Add ?name=PERSON_NAME to test photo fetch by name (fallback)',
                'examples': [
                    '?id=1001 (test Burak)',
                    '?id=1002 (test Ali)', 
                    '?name=Burak (fallback method)'
                ]
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


class AlertPersonViewSet(viewsets.ModelViewSet):
    serializer_class = AlertPersonSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['name']
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Return objects for the currently authenticated user only
        return AlertPerson.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def add_photo(self, request, pk=None):
        person = self.get_object()
        serializer = AlertPhotoSerializer(data=request.data)
        if serializer.is_valid():
            photo = serializer.save(person=person)
            image_vector = calculate_image_vector(photo.photo.path)
            photo.image_vector = image_vector
            photo.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AlarmViewSet(viewsets.ModelViewSet):
    queryset = Alarm.objects.all()
    serializer_class = AlarmSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['person__name', 'camera__name', 'date']

    @action(detail=False, methods=['post'])
    def cleanup(self, request):
        # Implement synchronous cleanup logic here
        return Response(status=status.HTTP_202_ACCEPTED)

@login_required
def alert_list(request):
    alerts = Alarm.objects.filter(
        Q(person__user=request.user) | Q(person__user__isnull=True)
    ).select_related(
        'person', 'camera', 'camera__venue', 'alert_photo', 'venue'
    ).order_by('-date')
    
    # Initialize filter form
    filter_form = AlertFilterForm(user=request.user, data=request.GET)
    
    if filter_form.is_valid():
        # Apply venue filter
        if filter_form.cleaned_data.get('venue'):
            venue = filter_form.cleaned_data['venue']
            alerts = alerts.filter(
                Q(venue=venue) | Q(camera__venue=venue)
            )
        
        # Apply person name filter
        if filter_form.cleaned_data.get('person_name'):
            person_name = filter_form.cleaned_data['person_name']
            alerts = alerts.filter(person__name__icontains=person_name)
        
        # Apply alarm type filter
        if filter_form.cleaned_data.get('alarm_type'):
            alarm_type = filter_form.cleaned_data['alarm_type']
            alerts = alerts.filter(alarm_type=alarm_type)
        
        # Apply date range filters
        if filter_form.cleaned_data.get('date_from'):
            date_from = filter_form.cleaned_data['date_from']
            alerts = alerts.filter(date__date__gte=date_from)
        
        if filter_form.cleaned_data.get('date_to'):
            date_to = filter_form.cleaned_data['date_to']
            alerts = alerts.filter(date__date__lte=date_to)
        
        # Apply person type filter
        if filter_form.cleaned_data.get('person_type'):
            person_type = filter_form.cleaned_data['person_type']
            if person_type == 'known':
                alerts = alerts.filter(person__is_unknown=False)
            elif person_type == 'unknown':
                alerts = alerts.filter(person__is_unknown=True)
    
    paginator = Paginator(alerts, 10)  # 10 alerts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/alert_list.html', {
        'page_obj': page_obj,
        'filter_form': filter_form,
        'total_alerts': alerts.count()
    })

@login_required
def unknown_persons_list(request):
    """View to list all unknown persons"""
    unknown_persons = AlertPerson.objects.filter(
        user=request.user,
        is_unknown=True
    ).order_by('-last_seen_date')

    paginator = Paginator(unknown_persons, 10)  # Show 10 unknown persons per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'alerts/unknown_persons_list.html', {
        'page_obj': page_obj,
    })

@login_required
def unknown_person_detail(request, person_id):
    """View to show details of an unknown person and their photos"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    
    # Initialize the form for renaming with user parameter
    form = RenameUnknownPersonForm(user=request.user)
    
    paginator = Paginator(photos, 5)  # Show 5 photos per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

@login_required
def rename_unknown_person(request, person_id):
    """View to rename an unknown person"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    
    if request.method == 'POST':
        form = RenameUnknownPersonForm(data=request.POST, user=request.user)
        if form.is_valid():
            old_name = person.name
            rename_type = form.cleaned_data['rename_type']
            
            if rename_type == 'new':
                # Create new person
                new_name = form.cleaned_data['new_name']
                success = transfer_unknown_to_new_person(person, new_name)
            else:
                # Merge with existing person
                existing_person = form.cleaned_data['existing_person']
                success = transfer_unknown_to_existing_person(person, existing_person)
                new_name = existing_person.name
            
            if success:
                messages.success(request, f'Person {old_name} successfully transferred to {new_name}.')
                return redirect('alerts:person_list')
            else:
                messages.error(request, 'Failed to transfer the person. Please try again.')
    else:
        form = RenameUnknownPersonForm(user=request.user)
    
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    paginator = Paginator(photos, 5)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

def transfer_unknown_to_new_person(unknown_person, new_name):
    """Transfer unknown person to a new known person"""
    try:
        old_name = unknown_person.name
        logger.info(f"Transferring unknown person {old_name} to new person {new_name}")
        
        # Handle embeddings
        success = handle_person_embeddings(old_name, new_name, merge_to_existing=False)
        if not success:
            logger.error(f"Failed to handle embeddings for {old_name}")
            return False
        
        # Move filesystem folders
        success = move_person_folder(old_name, new_name, merge_to_existing=False)
        if not success:
            logger.error(f"Failed to move folder for {old_name}")
            # Rollback embedding transfer if folder move fails
            rollback_embedding_transfer(new_name)
            return False
        
        # Create new known person
        new_person = AlertPerson.objects.create(
            user=unknown_person.user,
            name=new_name,
            is_unknown=False,
            first_seen_camera=unknown_person.first_seen_camera,
            last_seen_date=unknown_person.last_seen_date,
            created_at=unknown_person.created_at,
            updated_at=unknown_person.updated_at
        )
        
        # Transfer photos and alarms
        AlertPhoto.objects.filter(person=unknown_person).update(person=new_person)
        Alarm.objects.filter(person=unknown_person).update(person=new_person)
        
        # Update file paths in database
        update_file_paths(new_person, old_name, new_name)
        
        # Delete old unknown person
        unknown_person.delete()
        
        logger.info(f"Successfully transferred {old_name} to new person {new_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error transferring unknown person {old_name}: {str(e)}")
        return False

def transfer_unknown_to_existing_person(unknown_person, existing_person):
    """Transfer unknown person to an existing known person"""
    try:
        old_name = unknown_person.name
        existing_name = existing_person.name
        logger.info(f"Transferring unknown person {old_name} to existing person {existing_name}")
        
        # Handle embeddings (merge with existing)
        success = handle_person_embeddings(old_name, existing_name, merge_to_existing=True)
        if not success:
            logger.error(f"Failed to handle embeddings for {old_name}")
            return False
        
        # Move photos from unknown folder to existing person folder
        success = move_person_folder(old_name, existing_name, merge_to_existing=True)
        if not success:
            logger.error(f"Failed to merge folder for {old_name}")
            return False
        
        # Transfer photos and alarms to existing person
        AlertPhoto.objects.filter(person=unknown_person).update(person=existing_person)
        Alarm.objects.filter(person=unknown_person).update(person=existing_person)
        
        # Update file paths in database
        update_file_paths(existing_person, old_name, existing_name)
        
        # Delete old unknown person
        unknown_person.delete()
        
        logger.info(f"Successfully transferred {old_name} to existing person {existing_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error transferring unknown person {old_name} to existing person {existing_name}: {str(e)}")
        return False

def transfer_embeddings_from_unknown(unknown_name, new_name):
    """Transfer embeddings from unknown facebank to main facebank"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        facenet = FaceNetRecognizer()
        
        # Get embeddings from unknown facebank
        unknown_embeddings = get_unknown_person_embeddings(unknown_name)
        if unknown_embeddings is None:
            logger.error(f"No embeddings found for unknown person {unknown_name}")
            return False
        
        # Add embeddings to main facebank
        success = add_embeddings_to_main_facebank(new_name, unknown_embeddings)
        if not success:
            logger.error(f"Failed to add embeddings to main facebank for {new_name}")
            return False
        
        logger.info(f"Successfully transferred embeddings from {unknown_name} to {new_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error transferring embeddings from {unknown_name}: {str(e)}")
        return False

def merge_embeddings_to_existing(unknown_name, existing_name):
    """Merge embeddings from unknown facebank into existing person in main facebank"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        facenet = FaceNetRecognizer()
        
        # Get embeddings from unknown facebank
        unknown_embeddings = get_unknown_person_embeddings(unknown_name)
        if unknown_embeddings is None:
            logger.error(f"No embeddings found for unknown person {unknown_name}")
            return False
        
        # Merge embeddings with existing person in main facebank
        success = merge_embeddings_to_main_facebank(existing_name, unknown_embeddings)
        if not success:
            logger.error(f"Failed to merge embeddings to main facebank for {existing_name}")
            return False
        
        logger.info(f"Successfully merged embeddings from {unknown_name} into {existing_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error merging embeddings from {unknown_name}: {str(e)}")
        return False

def get_unknown_person_embeddings(unknown_name):
    """Get embeddings for an unknown person from unknown facebank"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        facenet = FaceNetRecognizer()
        if not hasattr(facenet, 'unknown_manager') or facenet.unknown_manager is None:
            logger.error("Unknown manager not available")
            return None
        
        unknown_manager = facenet.unknown_manager
        
        if unknown_manager.names is None or len(unknown_manager.names) == 0:
            logger.warning("No unknown persons in facebank")
            return None
        
        # Find indices for this unknown person
        person_indices = [i for i, name in enumerate(unknown_manager.names) if name == unknown_name]
        
        if not person_indices:
            logger.warning(f"Unknown person {unknown_name} not found in unknown facebank")
            return None
        
        # Extract embeddings for this person
        if unknown_manager.embeddings is not None:
            person_embeddings = unknown_manager.embeddings[person_indices]
            logger.info(f"Found {len(person_indices)} embeddings for {unknown_name}")
            return person_embeddings
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting embeddings for {unknown_name}: {str(e)}")
        return None

def add_embeddings_to_main_facebank(person_name, embeddings):
    """Add embeddings to main facebank for a new person"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        import torch
        import numpy as np
        
        facenet = FaceNetRecognizer()
        
        # Ensure facenet is loaded
        if facenet.embeddings is None or facenet.names is None:
            facenet.prepare_facebank()
        
        # Add embeddings to main facebank
        if facenet.embeddings is None:
            facenet.embeddings = embeddings
            facenet.names = np.array([person_name] * embeddings.shape[0])
        else:
            facenet.embeddings = torch.cat([facenet.embeddings, embeddings])
            new_names = np.array([person_name] * embeddings.shape[0])
            facenet.names = np.concatenate([facenet.names, new_names])
        
        # Save updated facebank
        torch.save(facenet.embeddings, os.path.join(facenet.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(facenet.conf['facebank_path'], 'names.npy'), facenet.names)
        
        logger.info(f"Added {embeddings.shape[0]} embeddings for {person_name} to main facebank")
        return True
        
    except Exception as e:
        logger.error(f"Error adding embeddings to main facebank for {person_name}: {str(e)}")
        return False

def merge_embeddings_to_main_facebank(existing_name, new_embeddings):
    """Merge new embeddings with existing person in main facebank"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        import torch
        import numpy as np
        
        facenet = FaceNetRecognizer()
        
        # Ensure facenet is loaded
        if facenet.embeddings is None or facenet.names is None:
            facenet.prepare_facebank()
        
        # Add new embeddings to main facebank for existing person
        if facenet.embeddings is None:
            facenet.embeddings = new_embeddings
            facenet.names = np.array([existing_name] * new_embeddings.shape[0])
        else:
            facenet.embeddings = torch.cat([facenet.embeddings, new_embeddings])
            additional_names = np.array([existing_name] * new_embeddings.shape[0])
            facenet.names = np.concatenate([facenet.names, additional_names])
        
        # Save updated facebank
        torch.save(facenet.embeddings, os.path.join(facenet.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(facenet.conf['facebank_path'], 'names.npy'), facenet.names)
        
        logger.info(f"Merged {new_embeddings.shape[0]} new embeddings for {existing_name} in main facebank")
        return True
        
    except Exception as e:
        logger.error(f"Error merging embeddings to main facebank for {existing_name}: {str(e)}")
        return False

def remove_from_unknown_facebank(unknown_name):
    """Remove person from unknown facebank"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        facenet = FaceNetRecognizer()
        if hasattr(facenet, 'unknown_manager') and facenet.unknown_manager is not None:
            success = facenet.unknown_manager.remove_unknown_person(unknown_name)
            if success:
                logger.info(f"Successfully removed {unknown_name} from unknown facebank")
            else:
                logger.warning(f"Failed to remove {unknown_name} from unknown facebank")
            return success
        else:
            logger.warning("Unknown manager not available")
            return False
            
    except Exception as e:
        logger.error(f"Error removing {unknown_name} from unknown facebank: {str(e)}")
        return False

def rollback_embedding_transfer(person_name):
    """Rollback embedding transfer in case of failure"""
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        import torch
        import numpy as np
        
        facenet = FaceNetRecognizer()
        
        if facenet.embeddings is None or facenet.names is None:
            return
        
        # Remove all embeddings for this person
        person_indices = [i for i, name in enumerate(facenet.names) if name == person_name]
        
        if person_indices:
            # Remove in reverse order to maintain indices
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(facenet.names) > 1:
                        facenet.embeddings = facenet.embeddings[1:]
                        facenet.names = facenet.names[1:]
                    else:
                        facenet.embeddings = None
                        facenet.names = np.array([])
                        break
                else:
                    facenet.embeddings = torch.cat([facenet.embeddings[:idx], facenet.embeddings[idx+1:]])
                    facenet.names = np.concatenate([facenet.names[:idx], facenet.names[idx+1:]])
            
            # Save updated facebank
            torch.save(facenet.embeddings, os.path.join(facenet.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(facenet.conf['facebank_path'], 'names.npy'), facenet.names)
            
            logger.info(f"Rolled back embedding transfer for {person_name}")
        
    except Exception as e:
        logger.error(f"Error during rollback for {person_name}: {str(e)}")

def update_person_embeddings(person_name):
    """Update only the embeddings for a specific person instead of rebuilding the entire facebank"""
    try:
        # Update FaceNet recognizer
        facenet = FaceNetRecognizer()
        facenet.update_person_embeddings(person_name)
        
        return True
    except Exception as e:
        logger.error(f"Error updating person embeddings: {str(e)}")
        return False


def update_photo_paths(person, old_name, new_name):
    """Update photo paths in database after renaming a person"""
    try:
        photos = AlertPhoto.objects.filter(person=person)
        updated_count = 0
        
        for photo in photos:
            if photo.photo:
                old_path = photo.photo.name
                
                # Update path from unknowns/old_name to new_name
                if f'unknowns/{old_name}/' in old_path:
                    # Replace unknowns/old_name/ with new_name/
                    new_path = old_path.replace(f'unknowns/{old_name}/', f'{new_name}/')
                elif f'unknowns\\{old_name}\\' in old_path:  # Windows path separator
                    new_path = old_path.replace(f'unknowns\\{old_name}\\', f'{new_name}\\')
                else:
                    # Fallback: construct new path
                    filename = os.path.basename(old_path)
                    new_path = os.path.join('alert_photos', new_name, filename).replace('\\', '/')
                
                photo.photo.name = new_path
                photo.save(update_fields=['photo'])
                updated_count += 1
                logger.debug(f"Updated photo path: {old_path} -> {new_path}")
        
        logger.info(f"Updated {updated_count} photo paths for {old_name} -> {new_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating photo paths: {str(e)}")
        return False

def update_alarm_paths(person, old_name, new_name):
    """Update alarm video snapshot paths in database after renaming a person"""
    try:
        alarms = Alarm.objects.filter(person=person)
        updated_count = 0
        
        for alarm in alarms:
            if alarm.video_snapshot:
                old_path = alarm.video_snapshot.name
                
                # Update path from unknowns/old_name to new_name
                if f'unknowns/{old_name}/' in old_path:
                    new_path = old_path.replace(f'unknowns/{old_name}/', f'{new_name}/')
                elif f'unknowns\\{old_name}\\' in old_path:  # Windows path separator
                    new_path = old_path.replace(f'unknowns\\{old_name}\\', f'{new_name}\\')
                else:
                    # Fallback: construct new path
                    filename = os.path.basename(old_path)
                    new_path = os.path.join('alert_photos', new_name, filename).replace('\\', '/')
                
                alarm.video_snapshot.name = new_path
                alarm.save(update_fields=['video_snapshot'])
                updated_count += 1
                logger.debug(f"Updated alarm snapshot path: {old_path} -> {new_path}")
        
        logger.info(f"Updated {updated_count} alarm snapshot paths for {old_name} -> {new_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating alarm paths: {str(e)}")
        return False

def update_file_paths(person, old_name, new_name):
    """Update both photo and alarm paths in database after renaming a person"""
    try:
        # Update photo paths
        photo_success = update_photo_paths(person, old_name, new_name)
        
        # Update alarm video snapshot paths  
        alarm_success = update_alarm_paths(person, old_name, new_name)
        
        return photo_success and alarm_success
        
    except Exception as e:
        logger.error(f"Error updating file paths: {str(e)}")
        return False

def move_person_folder(old_name, new_name, merge_to_existing=False):
    """Move or merge person folder from unknowns to known directory"""
    try:
        # Paths
        base_dir = settings.MEDIA_ROOT
        unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')
        known_dir = os.path.join(base_dir, 'alert_photos')
        
        # Source and destination paths
        src_path = os.path.join(unknown_dir, old_name)
        dst_path = os.path.join(known_dir, new_name)
        
        # Check if source directory exists
        if not os.path.exists(src_path):
            logger.warning(f"Source directory {src_path} does not exist, nothing to move")
            return True  # Not an error if no photos exist
        
        # For merging to existing, destination may already exist
        if merge_to_existing:
            logger.info(f"Merging folder: {src_path} -> {dst_path}")
        else:
            # For new person, ensure destination doesn't exist
            if os.path.exists(dst_path):
                logger.error(f"Destination directory {dst_path} already exists")
                return False
            logger.info(f"Moving folder: {src_path} -> {dst_path}")
        
        # Copy all contents to destination
        success = safe_copy_directory_contents(src_path, dst_path)
        if not success:
            logger.error(f"Failed to copy contents from {src_path} to {dst_path}")
            return False
        
        # Remove source directory with retries
        success = safe_remove_directory(src_path)
        if not success:
            logger.error(f"Failed to remove source directory {src_path}")
            if not merge_to_existing:
                # Clean up destination directory since operation failed
                safe_remove_directory(dst_path)
                return False
            else:
                # For merge, don't fail if we can't remove source - files were copied successfully
                logger.warning(f"Source directory {src_path} could not be removed but files were copied successfully")
        
        action = "merged" if merge_to_existing else "moved"
        logger.info(f"Successfully {action} folder from {old_name} to {new_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error moving person folder: {str(e)}")
        return False

def handle_person_embeddings(old_name, new_name, merge_to_existing=False):
    """Handle embedding operations for person renaming"""
    try:
        if merge_to_existing:
            success = merge_embeddings_to_existing(old_name, new_name)
            if success:
                logger.info(f"Successfully merged embeddings from {old_name} into {new_name}")
        else:
            success = transfer_embeddings_from_unknown(old_name, new_name)
            if success:
                logger.info(f"Successfully transferred embeddings from {old_name} to {new_name}")
        
        if success:
            # Remove from unknown facebank
            remove_from_unknown_facebank(old_name)
            
        return success
        
    except Exception as e:
        logger.error(f"Error handling embeddings: {str(e)}")
        return False

def force_refresh_person_counts(person):
    """Force refresh photo and alarm counts for a person"""
    try:
        from django.db.models import Count
        
        # Count photos
        photo_count = AlertPhoto.objects.filter(person=person).count()
        
        # Count alarms
        alarm_count = Alarm.objects.filter(person=person).count()
        
        # Update last seen date to current time to trigger any cached data refresh
        from django.utils import timezone
        person.last_seen_date = timezone.now()
        person.save(update_fields=['last_seen_date'])
        
        logger.info(f"Person {person.name} - Photos: {photo_count}, Alarms: {alarm_count}")
        return True
        
    except Exception as e:
        logger.error(f"Error refreshing person counts: {str(e)}")
        return False

@login_required
def delete_photo(request, photo_id):
    photo = get_object_or_404(AlertPhoto, id=photo_id)
    
    # Kullanıcının bu fotoğrafa erişim yetkisini kontrol et
    if photo.person.user != request.user:
        messages.error(request, 'You do not have permission to delete this photo.')
        return redirect('alerts:person_detail', person_id=photo.person.id)
    
    person_id = photo.person.id
    person_name = photo.person.name
    
    if request.method == 'POST':
        # Fotoğraf dosyasını silmek için
        if photo.photo:
            if os.path.isfile(photo.photo.path):
                os.remove(photo.photo.path)
                
        # Fotoğrafı veritabanından sil
        photo.delete()
        messages.success(request, 'Photo deleted successfully.')
        
        # Bu kişinin diğer fotoğrafları var mı kontrol et
        remaining_photos = AlertPhoto.objects.filter(person_id=person_id).exists()
        
        # Eğer primary foto sildiysen ve başka foto varsa, birini primary yap
        if not AlertPhoto.objects.filter(person_id=person_id, is_primary=True).exists() and remaining_photos:
            first_photo = AlertPhoto.objects.filter(person_id=person_id).first()
            if first_photo:
                first_photo.is_primary = True
                first_photo.save()
        
        # FaceNet model için bu kişinin embeddings verilerini güncelle
        try:
            # FaceNet için
            from recognition.facenet_recognizer import FaceNetRecognizer
            facenet_recognizer = FaceNetRecognizer()
            
            # Kişi için embeddings'leri güncelle
            facenet_recognizer.update_person_embeddings(person_name)
            
            logger.info(f"Updated embeddings for person {person_name} after photo deletion")
        except Exception as e:
            logger.error(f"Error updating embeddings for person {person_name}: {str(e)}")
        
        return redirect('alerts:person_detail', person_id=person_id)
    
    # GET isteği için onay sayfasını göster
    return render(request, 'alerts/confirm_photo_delete.html', {'photo': photo})

@login_required
def delete_selected_persons(request):
    if request.method == 'POST':
        selected_persons = request.POST.getlist('selected_persons')
        if selected_persons:
            from recognition.facenet_recognizer import FaceNetRecognizer
            
            # FaceNet modelini başlat
            facenet_recognizer = FaceNetRecognizer()
            
            deleted_persons = []
            
            for person_id in selected_persons:
                try:
                    # Kişiyi veritabanından al
                    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
                    person_name = person.name
                    
                    # Kişiye ait fotoğraf sayısını kontrol et
                    photos = AlertPhoto.objects.filter(person=person)
                    has_photos = photos.exists()
                    
                    # Kişiye ait tüm fotoğrafları sil (varsa)
                    if has_photos:
                        for photo in photos:
                            if photo.photo and os.path.isfile(photo.photo.path):
                                os.remove(photo.photo.path)
                    
                    # Kişiye ait model dosyalarını sil - Kişinin kendi klasörü (sadece fotoğrafı varsa)
                    if has_photos:
                        facebank_path = settings.RECOGNITION_CONFIG['facebank_path']
                        
                        # Kişinin kendi klasörünü sil (fotoğraflar)
                        person_dir = os.path.join(facebank_path, person_name)
                        if os.path.exists(person_dir):
                            logger.info(f"Deleting directory for person {person_name}: {person_dir}")
                            shutil.rmtree(person_dir)
                        
                        # Bilinmeyen kişiyse, unknowns klasöründe de kontrol et
                        if person.is_unknown:
                            unknown_dir = os.path.join(facebank_path, 'unknowns', person_name)
                            if os.path.exists(unknown_dir):
                                logger.info(f"Deleting unknown directory for person {person_name}: {unknown_dir}")
                                shutil.rmtree(unknown_dir)
                            
                            # Unknown facebank'tan da sil
                            try:
                                unknown_removed = facenet_recognizer.unknown_manager.remove_unknown_person(person_name)
                                if unknown_removed:
                                    logger.info(f"Successfully removed {person_name} from unknown facebank")
                                else:
                                    logger.warning(f"Failed to remove {person_name} from unknown facebank")
                            except Exception as e:
                                logger.error(f"Error removing {person_name} from unknown facebank: {str(e)}")
                        
                        # Model dosyalarından kaldır - Bu işlemi kişiyi silmeden önce yap (sadece fotoğrafı varsa)
                        logger.info(f"Updating model embeddings for person {person_name}")
                        facenet_success = facenet_recognizer.update_person_embeddings(person_name)
                        
                        if not facenet_success:
                            logger.warning(f"Failed to update FaceNet embeddings for {person_name}")
                    else:
                        # Fotoğrafı olmayan kişi için log
                        logger.info(f"Person {person_name} has no photos, skipping model cleanup")
                    
                    # Kişiyi veritabanından sil (alarmlar cascade ile silinecek)
                    person.delete()
                    
                    # Model dosyalarının temizlendiğini doğrula (sadece fotoğrafı varsa)
                    if has_photos:
                        cleanup_verified = verify_and_cleanup_model_files(person_name, logger)
                        if cleanup_verified:
                            deleted_persons.append(person_name)
                        else:
                            logger.warning(f"Model cleanup may be incomplete for person: {person_name}")
                            deleted_persons.append(f"{person_name} (model cleanup warning)")
                    else:
                        # Fotoğrafı olmayan kişi başarıyla silindi
                        deleted_persons.append(person_name)
                        logger.info(f"Successfully deleted person without photos: {person_name}")
                    
                except Exception as e:
                    logger.error(f"Error deleting person {person_id}: {str(e)}")
                    messages.error(request, f"Error deleting person: {str(e)}")
            
            # Başarılı mesajı
            if deleted_persons:
                logger.info(f"Successfully deleted persons: {', '.join(deleted_persons)}")
                messages.success(request, f"{len(deleted_persons)} person(s) and related data deleted successfully.")
        else:
            messages.warning(request, "No persons were selected.")
    
    return redirect('alerts:person_list')

def verify_and_cleanup_model_files(person_name, logger=None):
    """
    Helper function to verify and clean up model files for a deleted person
    Returns True if cleanup was successful, False otherwise
    """
    if not logger:
        import logging
        logger = logging.getLogger(__name__)
    
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        # Initialize recognizer
        facenet_recognizer = FaceNetRecognizer()
        
        # Check if person still exists in model files and remove if necessary
        facenet_cleaned = False
            
        # Check FaceNet model files  
        if facenet_recognizer.names is not None and person_name in facenet_recognizer.names:
            logger.warning(f"Person {person_name} still found in FaceNet model, attempting cleanup")
            facenet_cleaned = facenet_recognizer.update_person_embeddings(person_name)
        else:
            facenet_cleaned = True
        
        if facenet_cleaned:
            logger.info(f"Model cleanup verified successful for person: {person_name}")
            return True
        else:
            logger.error(f"Model cleanup failed for person: {person_name}")
            return False
            
    except Exception as e:
        logger.error(f"Error during model cleanup verification for {person_name}: {str(e)}")
        return False

def force_remove_readonly(func, path, excinfo):
    """Force remove readonly files on Windows"""
    try:
        os.chmod(path, stat.S_IWRITE)
        func(path)
    except Exception as e:
        logger.warning(f"Could not remove {path}: {str(e)}")

def safe_remove_directory(directory_path, max_retries=3, retry_delay=1):
    """Safely remove directory with retries for Windows"""
    for attempt in range(max_retries):
        try:
            if os.path.exists(directory_path):
                # On Windows, use onerror parameter to handle readonly files
                if os.name == 'nt':  # Windows
                    shutil.rmtree(directory_path, onerror=force_remove_readonly)
                else:
                    shutil.rmtree(directory_path)
                logger.info(f"Successfully removed directory: {directory_path}")
                return True
            else:
                logger.info(f"Directory does not exist: {directory_path}")
                return True
                
        except PermissionError as e:
            logger.warning(f"Permission error removing {directory_path} (attempt {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            else:
                logger.error(f"Failed to remove directory after {max_retries} attempts: {directory_path}")
                return False
        except Exception as e:
            logger.error(f"Error removing directory {directory_path}: {str(e)}")
            return False
    
    return False

def safe_copy_directory_contents(src_path, dst_path):
    """Safely copy directory contents with proper error handling"""
    try:
        # Create destination directory
        os.makedirs(dst_path, exist_ok=True)
        
        # Copy all contents recursively
        for root, dirs, files in os.walk(src_path):
            # Get relative path from source
            rel_path = os.path.relpath(root, src_path)
            
            # Create corresponding directory in destination
            if rel_path != '.':
                dst_dir = os.path.join(dst_path, rel_path)
                os.makedirs(dst_dir, exist_ok=True)
            
            # Copy files
            for file in files:
                src_file = os.path.join(root, file)
                if rel_path == '.':
                    dst_file = os.path.join(dst_path, file)
                else:
                    dst_file = os.path.join(dst_path, rel_path, file)
                
                # Generate unique filename if file already exists
                if os.path.exists(dst_file):
                    base_name, ext = os.path.splitext(file)
                    counter = 1
                    while os.path.exists(dst_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        if rel_path == '.':
                            dst_file = os.path.join(dst_path, new_filename)
                        else:
                            dst_file = os.path.join(dst_path, rel_path, new_filename)
                        counter += 1
                
                try:
                    shutil.copy2(src_file, dst_file)
                    logger.debug(f"Copied: {src_file} -> {dst_file}")
                except Exception as e:
                    logger.error(f"Failed to copy {src_file}: {str(e)}")
                    return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error copying directory contents from {src_path} to {dst_path}: {str(e)}")
        return False
     
# ============================
# VENUE MANAGEMENT VIEWS
# ============================

@login_required
def venue_list(request):
    """Venue listesi - kullanıcının venue'larını listele"""
    venues = Venue.objects.filter(user=request.user).order_by('name')
    
    # Seçili venue'ları silme işlemi
    if request.method == 'POST':
        venue_ids = request.POST.getlist('venue_ids')
        if venue_ids:
            deleted_count = Venue.objects.filter(id__in=venue_ids, user=request.user).count()
            Venue.objects.filter(id__in=venue_ids, user=request.user).delete()
            messages.success(request, f'{deleted_count} venue başarıyla silindi.')
        return redirect('alerts:venue_list')
    
    context = {
        'venues': venues,
    }
    return render(request, 'alerts/venue_list.html', context)


@login_required
def venue_create(request):
    """Yeni venue oluştur"""
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        
        if name:
            venue = Venue.objects.create(
                user=request.user,
                name=name,
                description=description,
                is_active=is_active
            )
            messages.success(request, f'Venue "{venue.name}" başarıyla oluşturuldu.')
            return redirect('alerts:venue_list')
        else:
            messages.error(request, 'Venue adı zorunludur.')
    
    return render(request, 'alerts/venue_create.html')


@login_required
def venue_edit(request, venue_id):
    """Venue düzenle"""
    venue = get_object_or_404(Venue, id=venue_id, user=request.user)
    
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        
        if name:
            venue.name = name
            venue.description = description
            venue.is_active = is_active
            venue.save()
            messages.success(request, f'Venue "{venue.name}" başarıyla güncellendi.')
            return redirect('alerts:venue_list')
        else:
            messages.error(request, 'Venue adı zorunludur.')
    
    context = {
        'venue': venue,
        'camera_count': venue.cameras.count(),
    }
    return render(request, 'alerts/venue_edit.html', context)


@login_required
def venue_delete(request, venue_id):
    """Venue sil"""
    venue = get_object_or_404(Venue, id=venue_id, user=request.user)
    
    if request.method == 'POST':
        venue_name = venue.name
        venue.delete()
        messages.success(request, f'Venue "{venue_name}" başarıyla silindi.')
        return redirect('alerts:venue_list')
    
    context = {
        'venue': venue,
        'camera_count': venue.cameras.count(),
    }
    return render(request, 'alerts/venue_delete.html', context)


# Employee Sync Views
def sync_employees(request):
    """Employee synchronization page"""
    context = {
        'api_config': employee_photo_service.get_cache_stats(),
        'existing_employees': AlertPerson.objects.filter(employee_id__isnull=False).count(),
    }
    return render(request, 'alerts/sync_employees.html', context)


@login_required
def sync_employees_ajax(request):
    """AJAX endpoint for employee synchronization - Two-way sync"""
    from django.http import JsonResponse
    from django.views.decorators.http import require_POST
    from django.db import models
    import json
    
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)
    
    try:
        # Fetch employees from API
        employees_data = employee_photo_service._fetch_employee_data()
        
        if not employees_data:
            return JsonResponse({
                'success': False,
                'error': 'Failed to fetch employees from API',
                'stats': {
                    'api_employees': 0,
                    'existing_employees': 0,
                    'new_employees': 0,
                    'updated_employees': 0,
                    'deleted_employees': 0
                }
            })
        
        # API'den gelen çalışan ID'lerini set yap
        api_employee_ids = set()
        for employee in employees_data:
            employee_id = str(employee.get('employeeId', ''))
            if employee_id:
                api_employee_ids.add(employee_id)
        
        # Mevcut veritabanındaki çalışanları al
        existing_employees = AlertPerson.objects.filter(employee_id__isnull=False)
        existing_employee_ids = set(existing_employees.values_list('employee_id', flat=True))
        
        new_employees = []
        updated_employees = []
        deleted_employees = []
        
        # YENİ ve GÜNCELLENECEK ÇALIŞANLAR
        for employee in employees_data:
            employee_id = str(employee.get('employeeId', ''))
            employee_name = employee.get('name', f'Employee {employee_id}')
            
            if not employee_id:
                continue
                
            if employee_id not in existing_employee_ids:
                # Create new employee
                new_person = AlertPerson.objects.create(
                    name=employee_name,
                    employee_id=employee_id,
                    is_unknown=False,
                    user_id=None  # API'den gelen için null
                )
                new_employees.append({
                    'id': new_person.id,
                    'name': employee_name,
                    'employee_id': employee_id
                })
            else:
                # Update existing employee if needed
                try:
                    existing_person = AlertPerson.objects.get(employee_id=employee_id)
                    if existing_person.name != employee_name:
                        existing_person.name = employee_name
                        existing_person.save()
                        updated_employees.append({
                            'id': existing_person.id,
                            'name': employee_name,
                            'employee_id': employee_id
                        })
                except AlertPerson.DoesNotExist:
                    pass
        
        # SİLİNEN ÇALIŞANLAR: DB'de olan ama API'de olmayan
        employees_to_delete = existing_employees.filter(
            ~models.Q(employee_id__in=api_employee_ids)
        )
        
        # Silme listesini hazırla
        for employee in employees_to_delete:
            deleted_employees.append({
                'id': employee.id,
                'name': employee.name,
                'employee_id': employee.employee_id
            })
        
        # Silinen çalışanları veritabanından kaldır
        deleted_count = employees_to_delete.delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'Employee synchronization completed: {len(new_employees)} added, {len(updated_employees)} updated, {deleted_count} deleted',
            'stats': {
                'api_employees': len(employees_data),
                'existing_employees': len(existing_employee_ids),
                'new_employees': len(new_employees),
                'updated_employees': len(updated_employees),
                'deleted_employees': deleted_count
            },
            'new_employees': new_employees,
            'updated_employees': updated_employees,
            'deleted_employees': deleted_employees
        })
        
    except Exception as e:
        logger.error(f"Employee sync error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Sync failed: {str(e)}',
            'stats': {
                'api_employees': 0,
                'existing_employees': 0,
                'new_employees': 0,
                'updated_employees': 0,
                'deleted_employees': 0
            }
        })
