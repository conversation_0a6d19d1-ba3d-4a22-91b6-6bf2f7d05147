# Generated by Django 5.1.4 on 2025-09-09 12:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_alter_userprofile_preferred_recognizer'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='keycloak_client_roles',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak client roles'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='keycloak_id',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='keycloak_realm_roles',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak realm roles'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='last_keycloak_sync',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='permissions',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of user permissions'),
        ),
    ]
