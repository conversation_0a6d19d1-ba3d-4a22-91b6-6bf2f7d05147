/* Sync Employees Page Styles */

/* Status Items */
.status-item {
    text-align: center;
    padding: 1rem;
}

.status-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Sync Progress */
.sync-progress {
    margin-top: 1rem;
}

.progress {
    height: 8px;
    background-color: #e9ecef;
}

.progress-status {
    font-size: 0.9rem;
    color: #6c757d;
    text-align: center;
}

/* Statistics Cards */
.stat-card {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem 1rem;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-card.api-employees {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.stat-card.existing-employees {
    border-color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.stat-card.new-employees {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.stat-card.updated-employees {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.stat-card.deleted-employees {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Card Enhancements */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

.card-header h5 {
    margin: 0;
}

.card-header i {
    margin-right: 0.5rem;
}

/* Button Styles */
.btn {
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-primary:disabled {
    background: #6c757d;
    transform: none;
}

/* Table Styles */
.table {
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 6px;
}

/* Code Styles */
code {
    color: #e83e8c;
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.85em;
}

/* Responsive */
@media (max-width: 768px) {
    .status-item {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
}

/* Animation Classes */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}