# Generated by Django 5.1.4 on 2025-09-29 08:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_remove_userprofile_keycloak_client_roles_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userprofile',
            name='assigned_by',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='role_assigned_date',
        ),
        migrations.AddField(
            model_name='userprofile',
            name='keycloak_client_roles',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak client roles'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='keycloak_realm_roles',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak realm roles'),
        ),
        migrations.Add<PERSON>ield(
            model_name='userprofile',
            name='permissions',
            field=models.TextField(blank=True, default='[]', help_text='JSON list of user permissions'),
        ),
        migrations.DeleteModel(
            name='UserRoleHistory',
        ),
    ]
