from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User, Group
from django.core.exceptions import ValidationError
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import UserProfile, UserRoleHistory


class UserProfileInline(admin.StackedInline):
    """Inline admin for UserProfile"""
    model = UserProfile
    fk_name = 'user'
    can_delete = False
    verbose_name_plural = 'Profile'
    fields = ('preferred_recognizer', 'is_organizational_admin', 'display_keycloak_user', 'keycloak_id', 'last_keycloak_sync')
    readonly_fields = ('display_keycloak_user', 'keycloak_id', 'last_keycloak_sync')

    def display_keycloak_user(self, obj):
        """Show human-readable Keycloak identity"""
        if obj.user and obj.user.username:
            return obj.user.username
        return obj.user.email or obj.keycloak_id or "-"
    display_keycloak_user.short_description = "Keycloak User"


class RoleHistoryInline(admin.TabularInline):
    """Inline admin for Role History"""
    model = UserRoleHistory
    fk_name = 'user'
    extra = 0
    readonly_fields = ('role_name', 'action', 'assigned_by', 'timestamp', 'reason')
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class CustomUserAdmin(BaseUserAdmin):
    """Custom User Admin with role management and restrictions"""
    inlines = (UserProfileInline, RoleHistoryInline)

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Groups and Permissions', {
            'fields': ('groups', 'user_permissions'),
            'description': 'Manage user roles and permissions. Roles are implemented as Django Groups.'
        }),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2'),
        }),
        ('Role Assignment', {
            'classes': ('wide',),
            'fields': ('groups',),
            'description': mark_safe(
                '<strong>Note:</strong> Admin/Superuser accounts can only be created via command line.<br/>'
                'Use: <code>python manage.py create_admin --username admin --password your_password</code>'
            )
        }),
    )

    list_display = ('username', 'email', 'first_name', 'last_name',
                    'is_active', 'display_roles', 'last_login')
    list_filter = list(BaseUserAdmin.list_filter) + ['groups']
    search_fields = ('username', 'first_name', 'last_name', 'email', 'groups__name')

    def display_roles(self, obj):
        """Display user's roles (groups) in the list view"""
        roles = obj.groups.all()
        if roles:
            role_links = []
            for role in roles:
                url = reverse('admin:auth_group_change', args=[role.pk])
                role_links.append(f'<a href="{url}">{role.name}</a>')
            return format_html(', '.join(role_links))
        return '-'
    display_roles.short_description = 'Roles'


class CustomGroupAdmin(admin.ModelAdmin):
    """Enhanced Group Admin for role management"""
    list_display = ('name', 'user_count', 'permission_count', 'description')
    search_fields = ('name',)
    filter_horizontal = ('permissions',)

    fieldsets = (
        (None, {'fields': ('name',)}),
        ('Permissions', {
            'fields': ('permissions',),
            'description': 'Select permissions for this role. Users assigned to this role will inherit these permissions.'
        }),
    )

    def user_count(self, obj):
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:auth_user_changelist') + f'?groups__id__exact={obj.pk}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return '0 users'
    user_count.short_description = 'Users'

    def permission_count(self, obj):
        return obj.permissions.count()
    permission_count.short_description = 'Permissions'

    def description(self, obj):
        descriptions = {
            'Camera_Viewer': 'Can view camera streams and alerts',
            'Camera_Operator': 'Can view and control camera streams, configure zones',
            'Camera_Manager': 'Full camera management including add/edit/delete',
            'Person_Manager': 'Manage persons and their photos',
            'System_Admin': 'Full system administration access'
        }
        return descriptions.get(obj.name, 'Custom role')
    description.short_description = 'Description'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = (
        'user',
        'preferred_recognizer',
        'is_organizational_admin',
        'display_roles',
        'last_keycloak_sync',
        'display_keycloak_user',  # username/email gösterir
    )
    search_fields = ('user__username', 'user__email')
    list_filter = ('is_organizational_admin', 'preferred_recognizer')
    
    readonly_fields = (
        'display_keycloak_user',
        'last_keycloak_sync',
        'assigned_by',
        'role_assigned_date',
    )
    
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'preferred_recognizer', 'is_organizational_admin'),
        }),
        ('Role Assignment', {
            'fields': ('assigned_by', 'role_assigned_date'),
            'classes': ('collapse',),
        }),
        ('Keycloak Integration', {
            'fields': ('display_keycloak_user', 'last_keycloak_sync'),
            'classes': ('collapse',),
        }),
    )

    def display_roles(self, obj):
        return ", ".join(obj.role_names)
    display_roles.short_description = "Roles"

    def display_keycloak_user(self, obj):
        """Sub yerine username (ve email) göster"""
        if obj.user:
            return f"{obj.user.username} ({obj.user.email})"
        return "-"
    display_keycloak_user.short_description = "Keycloak User"

@admin.register(UserRoleHistory)
class UserRoleHistoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'role_name', 'action', 'assigned_by', 'timestamp')
    search_fields = ('user__username', 'role_name')
    list_filter = ('action', 'timestamp')
    search_fields = ('user__username', 'role_name', 'assigned_by__username')
    readonly_fields = ('user', 'role_name', 'action', 'assigned_by', 'timestamp', 'reason')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def display_user(self, obj):
        if obj.user.username:
            return obj.user.username
        return obj.user.email or obj.user.id
    display_user.short_description = "User"


# Varsayılan adminleri unregister
admin.site.unregister(User)
admin.site.unregister(Group)

# Custom adminleri register
admin.site.register(User, CustomUserAdmin)
admin.site.register(Group, CustomGroupAdmin)

# Admin site headerları
admin.site.site_header = "Face Recognition System Administration"
admin.site.site_title = "Face Recognition Admin"
admin.site.index_title = "System Administration"
