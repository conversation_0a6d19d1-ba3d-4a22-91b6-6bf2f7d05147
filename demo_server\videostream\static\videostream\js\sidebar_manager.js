class SidebarManager {
    constructor(areaListElement, onEdit, onDelete) {
      this.areaList = areaListElement;
      this.onEdit = onEdit;
      this.onDelete = onDelete;
    }
  
    addPolygonLabel(name, color, polygon) {
      const div = document.createElement('div');
      div.className = 'area-item';
  
      const labelDiv = document.createElement('div');
      labelDiv.className = 'area-label';
  
      const colorBox = document.createElement('div');
      colorBox.className = 'color-box';
      colorBox.style = `width: 16px; height: 16px; margin-right: 6px; background: ${color}; border: 1px solid #999;`;
  
      const labelText = document.createElement('span');
      labelText.textContent = name;
      labelText.style.cursor = 'pointer';
  
      labelText.addEventListener('dblclick', () => {
        const input = document.createElement('input');
        input.type = 'text';
        input.value = labelText.textContent;
  
        input.addEventListener('blur', () => {
          labelText.textContent = input.value;
          polygon.name = input.value; // Update the polygon's name property
          labelDiv.replaceChild(labelText, input);
        });
  
        input.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') input.blur();
        });
  
        labelDiv.replaceChild(input, labelText);
        input.focus();
      });
  
      labelDiv.appendChild(colorBox);
      labelDiv.appendChild(labelText);
  
      const btnGroup = document.createElement('div');
      btnGroup.style = 'display: flex; gap: 5px; margin-top: 5px;';
  
      const editBtn = document.createElement('button');
      editBtn.textContent = 'Edit';
      editBtn.style = 'font-size: 12px; padding: 2px 6px; color: blue; border: 1px solid blue; background: white;';
      editBtn.onclick = (e) => {
        e.stopPropagation();
        this.onEdit(polygon);
      };
  
      const deleteBtn = document.createElement('button');
      deleteBtn.textContent = 'Delete';
      deleteBtn.style = 'font-size: 12px; padding: 2px 6px; color: red; border: 1px solid red; background: white;';
      deleteBtn.onclick = (e) => {
        e.stopPropagation();
        this.onDelete(polygon, div);
      };
  
      btnGroup.appendChild(editBtn);
      btnGroup.appendChild(deleteBtn);
      div.appendChild(labelDiv);
      div.appendChild(btnGroup);
  
      this.areaList.appendChild(div);
      polygon.element = div;
    }
  
    highlightActive(polygons, currentPolygon) {
      polygons.forEach(p => p.element?.classList.remove('active'));
      if (currentPolygon?.element) {
        currentPolygon.element.classList.add('active');
      }
    }
  
    removePolygonLabel(div) {
      this.areaList.removeChild(div);
    }
  }
  
window.SidebarManager = SidebarManager;
