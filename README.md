# Face Recognition

This repository provides a face recognition solution. Follow the steps below to set up and start using the project.

**Clone the Repository**

   ```bash
   git clone https://github.com/autodidactic-technologies/face_recognition.git
   ```
   ```bash
   cd face-recognition
   ```
--------------------------------------------------------------------------------------

**Set Up Conda Environment**

   ```bash
   conda create face_recognition python=3.12
   ```
   ```bash
   conda activate face_recognition
   ```
--------------------------------------------------------------------------------------

**Install Requirements**

```bash
pip install -r requirements.txt
```
--------------------------------------------------------------------------------------
**Download Model and Facebank Files**

[Model](https://onedrive.live.com/?authkey=%21AOw5TZL8cWlj10I&id=CEC0E1F8F0542A13%21835&cid=CEC0E1F8F0542A13&parId=root&parQt=sharedby&o=OneUp)

[Facebank](https://drive.google.com/drive/folders/1OTTcFqIL2qV5mqyjMyN4LejtKZMoBjS1)

--------------------------------------------------------------------------------------


**Set Up Directories**

Provide the ***model*** directory in the root of the project. Ensure it contains model file.

Provide the ***media*** and ***alert_photos*** folders as subdirectories. Ensure ***alert_photos*** directory contains the ***facebank*** images.

- **demo_server**
  - **media**
    - alert_photos
--------------------------------------------------------------------------------------

**Configure PostgreSQL**

1. Create the application user and database (adjust names/passwords to your environment):
   ```bash
   sudo -u postgres createuser --pwprompt face_app
   sudo -u postgres createdb --owner=face_app face_recognition
   ```
2. Copy ***.env.template*** to ***.env*** in the same directory and fill in the required fields. Besides the Keycloak settings, set:
   ```
   POSTGRES_DB=face_recognition
   POSTGRES_USER=face_app
   POSTGRES_PASSWORD=<your password>
   POSTGRES_HOST=127.0.0.1
   POSTGRES_PORT=5432
   ```
3. Apply database migrations once PostgreSQL is reachable:
   ```bash
   python manage.py migrate
   ```

--------------------------------------------------------------------------------------

**Set Up Keycloak**

Run the following docker command to start a Keycloak instance:

```bash
docker run -p 127.0.0.1:8080:8080 -e KC_BOOTSTRAP_ADMIN_USERNAME=admin -e KC_BOOTSTRAP_ADMIN_PASSWORD=admin quay.io/keycloak/keycloak:26.3.3 start-dev
```

After, watch the video below to see how to set up Keycloak configuration:

[Keycloak Setup](https://drive.google.com/file/d/1cAqmn68FmbvkH2mzjixHtzO52sDFQqko/view?usp=sharing)

--------------------------------------------------------------------------------------

**Run the Server**

In the ***demo_server*** folder you can start in two ways.

If you don't need websocket functionality `python manage.py runserver` is enough to see the webpages.

To see the full functional websocket alarms use `uvicorn webserver.asgi:application`. (Uvicorn should have been install with `pip install uvicorn daphne`)

--------------------------------------------------------------------------------------

**Video Streaming with FFMPEG**

For Linux: 
```bash
ffmpeg -f v4l2 -rtbufsize 300M -i /dev/video0 -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```

For Windows: 
```bash
ffmpeg -f dshow -rtbufsize 300M -i video="your_camera_name" -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```

For MacOs:
```
ffmpeg -f avfoundation -framerate pal -i "0:none"  -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```
