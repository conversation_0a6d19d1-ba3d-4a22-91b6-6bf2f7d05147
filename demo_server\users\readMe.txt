STEP 2: <PERSON><PERSON><PERSON><PERSON><PERSON>K SETUP
======================
1. Start Keycloak using Docker:
   docker run -p 127.0.0.1:8080:8080 -e KC_BOOTSTRAP_ADMIN_USERNAME=admin -e KC_BOOTSTRAP_ADMIN_PASSWORD=admin quay.io/keycloak/keycloak:26.3.3 start-dev

2. Access Keycloak Admin Console:
   - Open browser: http://localhost:8080
   - Login with: admin / admin

3. Create Realm:
   - Click "Create Realm" button
   - Name: "face-recognition"
   - Click "Create"

4. Create Client:
   - Go to "Clients" tab
   - Click "Create client"
   - Client ID: "face-recognition-django"
   - Client type: "OpenID Connect"
   - Click "Next"
   - Client authentication: ON
   - Authorization: OFF
   - Standard flow: ON
   - Direct access grants: ON
   - Click "Save"

5. Configure Client Settings:
   - Go to "Settings" tab of your client
   - Valid redirect URIs: http://localhost:8000/oidc/callback/
   - <PERSON><PERSON> post logout redirect URIs: http://localhost:8000/users/keycloak-logout/
   - Web origins: http://localhost:8000
   - Click "Save"

6. Get Client Secret:
   - Go to "Credentials" tab
   - Copy the "Client secret" value (you'll need this later)

7. Create Test User:
   - Go to "Users" tab
   - Click "Add user"
   - Username: testuser
   - Email: <EMAIL>
   - First name: Test
   - Last name: User
   - Email verified: ON
   - Click "Create"

8. Set User Password:
   - Go to "Credentials" tab of the user
   - Click "Set password"
   - Password: testpassword123
   - Temporary: OFF
   - Click "Save"

STEP 3: DJANGO ENVIRONMENT CONFIGURATION
=========================================
1. Create .env file in the project root:
   SECRET_KEY=your-django-secret-key-here
   DEBUG=True
   KEYCLOAK_CLIENT_ID=face-recognition-django
   KEYCLOAK_CLIENT_SECRET=your-keycloak-client-secret-from-step-2.6
   KEYCLOAK_SERVER_URL=http://127.0.0.1:8080
   KEYCLOAK_REALM=face-recognition

2. Generate Django Secret Key (if needed):
   python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"

STEP 4: DJANGO DATABASE SETUP
==============================
1. Navigate to Django project directory:
   cd demo_server

2. Run migrations:
   python manage.py makemigrations
   python manage.py migrate

3. Create Django superuser (Method 1 - Built-in):
   python manage.py createsuperuser
   # Follow prompts to enter username, email, and password

   OR

   Create Django superuser (Method 2 - Custom command):
   python manage.py create_admin --username admin --password admin123 --email <EMAIL>

STEP 5: DJANGO GROUPS AND PERMISSIONS SETUP
============================================
1. Start Django server:
   python manage.py runserver

2. Access Django Admin:
   - Open browser: http://localhost:8000/admin/
   - Login with the superuser credentials created in Step 4

3. Create User Roles (Groups):
   - Go to "Groups" section
   - Create the following groups with their permissions:

   GROUP: Camera_Viewer
   Permissions:
   - cameras | camera | Can view camera
   - videostream | zone | Can view zone
   - alerts | alert person | Can view alert person

   GROUP: Camera_Operator
   Permissions:
   - cameras | camera | Can view camera
   - videostream | zone | Can view zone
   - videostream | zone | Can change zone
   - videostream | zone | Can add zone
   - alerts | alert person | Can view alert person

   GROUP: Camera_Manager
   Permissions:
   - cameras | camera | Can add camera
   - cameras | camera | Can change camera
   - cameras | camera | Can delete camera
   - cameras | camera | Can view camera
   - videostream | zone | Can add zone
   - videostream | zone | Can change zone
   - videostream | zone | Can delete zone
   - videostream | zone | Can view zone
   - alerts | alert person | Can view alert person

   GROUP: Person_Manager
   Permissions:
   - alerts | alert person | Can add alert person
   - alerts | alert person | Can change alert person
   - alerts | alert person | Can delete alert person
   - alerts | alert person | Can view alert person
   - alerts | alert photo | Can add alert photo
   - alerts | alert photo | Can change alert photo
   - alerts | alert photo | Can delete alert photo
   - alerts | alert photo | Can view alert photo
   - alerts | alarm | Can add alarm
   - alerts | alarm | Can change alarm
   - alerts | alarm | Can delete alarm
   - alerts | alarm | Can view alarm

   GROUP: System_Admin
   Permissions:
   - Select ALL permissions available

STEP 6: TEST KEYCLOAK INTEGRATION
==================================
1. Logout from Django admin (if logged in)

2. Go to main application: http://localhost:8000/

3. Click "Login with Keycloak" button

4. You should be redirected to Keycloak login page

5. Login with the test user created in Step 2.7:
   - Username: testuser
   - Password: testpassword123

6. After successful login, you should be redirected back to the Django application

7. The user should now appear in Django Admin under "Users" section

STEP 7: ASSIGN ROLES TO KEYCLOAK USERS
=======================================
1. Go to Django Admin: http://localhost:8000/admin/

2. Login as superuser

3. Go to "Users" section

4. Find the Keycloak user (testuser)

5. Edit the user and assign appropriate groups:
   - Scroll to "Groups" section
   - Move desired groups from "Available groups" to "Chosen groups"
   - Click "Save"

STEP 8: VERIFY ROLE-BASED ACCESS
=================================
1. Logout and login again with the Keycloak user

2. Try accessing different parts of the application:
   - Camera management: http://localhost:8000/camera/
   - Alert management: http://localhost:8000/alert/
   - Video stream: http://localhost:8000/videostream/viewer/

3. Access should be granted/denied based on the assigned roles
