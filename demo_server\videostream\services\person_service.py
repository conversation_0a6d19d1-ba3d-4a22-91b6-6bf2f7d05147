import logging
from django.db import transaction
from django.core.files.base import ContentFile
from django.utils import timezone
from asgiref.sync import sync_to_async
import cv2

# Import models
from alerts.models import <PERSON><PERSON><PERSON><PERSON>, AlertPhoto

# Import unknown facebank manager
from recognition.unknown_facebank_manager import UnknownFacebankManager
from recognition.model_manager import ModelManager

logger = logging.getLogger(__name__)


class PersonService:
    """Service for handling person-related operations"""

    def __init__(self):
        # Initialize unknown facebank manager
        self.unknown_facebank_manager = None
        self._initialize_unknown_facebank_manager()

        # Keep the original unknown_count for backward compatibility
        self.unknown_count = self._get_latest_unknown_count()
        logger.info(f"PersonService initialized with unknown_count: {self.unknown_count}")

    def _initialize_unknown_facebank_manager(self):
        """Initialize the unknown facebank manager with model components"""
        try:
            # Get FaceNet model from ModelManager
            model_manager = ModelManager()
            facenet_recognizer = model_manager.get_model('facenet')

            # Extract model components from FaceNet recognizer
            if hasattr(facenet_recognizer, 'mtcnn') and hasattr(facenet_recognizer, 'model'):
                self.unknown_facebank_manager = UnknownFacebankManager(
                    mtcnn=facenet_recognizer.mtcnn,
                    model=facenet_recognizer.model
                )
                logger.info("✅ Unknown facebank manager initialized successfully")
            else:
                logger.warning("FaceNet recognizer missing mtcnn or model attributes")
                self.unknown_facebank_manager = UnknownFacebankManager()
                logger.warning("⚠️ Unknown facebank manager initialized without model components")

        except Exception as e:
            logger.error(f"❌ Failed to initialize unknown facebank manager: {str(e)}")
            # Initialize without model components as fallback
            self.unknown_facebank_manager = UnknownFacebankManager()
            logger.warning("⚠️ Unknown facebank manager initialized without model components")

    def _get_latest_unknown_count(self):
        """
        Get the latest unknown count from existing directories
        Following original working logic from git history
        """
        try:
            from django.conf import settings
            import os

            # Check unknowns directory like original logic
            base_dir = settings.MEDIA_ROOT
            unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')

            if not os.path.exists(unknown_dir):
                logger.info(f"Unknown directory doesn't exist, starting from 0")
                return 0

            max_number = 0

            # Scan existing Unknown_X directories
            for item in os.listdir(unknown_dir):
                item_path = os.path.join(unknown_dir, item)
                if os.path.isdir(item_path) and item.startswith("Unknown_"):
                    try:
                        number_part = item.split("_")[1]
                        number = int(number_part)
                        max_number = max(max_number, number)
                    except (ValueError, IndexError):
                        logger.warning(f"Malformed unknown directory name: {item}")
                        continue

            logger.info(f"Found {max_number} existing unknown persons in directories")
            return max_number

        except Exception as e:
            logger.error(f"Error getting latest unknown count: {str(e)}")
            return 0
    
    async def process_unknown_person(self, frame, camera):
        """
        Process and save unknown person using the unknown facebank system.
        This ensures proper ID tracking and embedding storage.

        Args:
            frame: Full video frame
            camera: Camera object

        Returns:
            tuple: (AlertPerson instance, person_name) or (None, None)
        """
        try:
            # Get next unknown ID from the facebank manager
            if self.unknown_facebank_manager:
                person_name = self.unknown_facebank_manager.get_next_unknown_id()
            else:
                # Fallback to original logic
                self.unknown_count += 1
                person_name = f"Unknown_{self.unknown_count}"

            logger.info(f"🔄 Processing unknown person: {person_name}")

            # Use FaceNetRecognizer's comprehensive _create_new_unknown_person method
            from recognition.facenet_recognizer import FaceNetRecognizer
            from PIL import Image
            
            try:
                # Convert frame to PIL Image for face processing
                face_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                
                # Create unknown person with full embedding and database support
                recognizer = FaceNetRecognizer()
                created_unknown_id = await sync_to_async(recognizer._create_new_unknown_person)(
                    face_image, camera_id=camera.id, full_frame=frame
                )
                
                if created_unknown_id:
                    # Get the created person from database
                    alert_person = await sync_to_async(AlertPerson.objects.get)(
                        name=created_unknown_id, is_unknown=True
                    )
                    
                    logger.info(f"✅ Unknown person created successfully: {created_unknown_id}")
                    logger.info(f"   - Database ID: {alert_person.id}")
                    logger.info(f"   - Directory created for: {created_unknown_id}")
                    return alert_person, created_unknown_id
                else:
                    logger.error(f"❌ Failed to create unknown person: {person_name}")
                    return None, None
                    
            except Exception as e:
                logger.error(f"❌ Error using FaceNetRecognizer: {str(e)}")
                return None, None

        except Exception as e:
            logger.error(f"❌ Error processing unknown person: {str(e)}")
            return None, None

    @staticmethod
    async def update_person_last_seen(person_name):
        """Update last seen date for a known person"""
        try:
            def get_person():
                return AlertPerson.objects.filter(name=person_name, is_unknown=False).first()
            
            person = await sync_to_async(get_person)()

            if person:
                from django.utils import timezone
                
                def update_person():
                    person.last_seen_date = timezone.now()
                    person.save()
                    return person
                    
                updated_person = await sync_to_async(update_person)()
                logger.debug(f"Updated last seen for {person_name}")
                return updated_person
            else:
                logger.warning(f"Person not found: {person_name}")
                return None

        except Exception as e:
            logger.error(f"Error updating last seen for {person_name}: {str(e)}")
            return None
