from .base_recognizer import BaseRecognizer
from .facenet_model import Backbone, MobileFaceNet, l2_norm
import torch
import numpy as np
from torchvision import transforms as tv_transforms
from PIL import Image
from .facenet_mtcnn import MTCNN
import cv2
import os
import io
from django.conf import settings
from asyncio.log import logger
from alerts.models import AlertPhoto, Alert<PERSON>erson
from cameras.models import Camera
from django.core.files.base import ContentFile
from django.db import transaction
import time
from videostream.logger import writer
from .unknown_facebank_manager import UnknownFacebankManager

class FaceNetRecognizer(BaseRecognizer):
    def __init__(self, mtcnn=None, model=None):
        """
        FaceNet tanıyıcı sınıfı - Dependency Injection yaklaşımı ile model bileşenlerini kabul eder
        Eğer model bileşenleri verilmezse kendisi yükler

        Args:
            mtcnn: Önceden yüklenmiş bir MTCNN nesnesi
            model: Önceden yüklenmiş bir FaceNet model nesnesi
        """
        logger.info("Initializing FaceNetRecognizer with dependency injection approach")
        self.conf = settings.RECOGNITION_CONFIG
        self.model_name = 'ir_se50.pth'

        # Model bileşenlerini yükle veya dışarıdan alınanları kullan
        self.mtcnn = mtcnn or self._load_mtcnn()
        self.model = model or self._load_model()

        # load facebank embeddings and names
        self._load_facebank()

        # Initialize unknown face bank manager with shared model references
        self.unknown_manager = UnknownFacebankManager(
            mtcnn=self.mtcnn,
            model=self.model
        )

        logger.info("FaceNetRecognizer initialization complete")

    def _load_mtcnn(self):
        """MTCNN yüz dedektörünü yükle"""
        logger.info("Loading FaceNet MTCNN detector")
        return MTCNN()
        
    def _load_model(self):
        """FaceNet modelini yükle"""
        logger.info("Loading FaceNet recognition model")
        
        if self.conf['use_mobilfacenet']:
            model = MobileFaceNet(self.conf['embedding_size']).to(self.conf['device'])
            logger.info('MobileFaceNet model generated')
        else:
            model = Backbone(self.conf['net_depth'], self.conf['drop_ratio'], self.conf['net_mode']).to(self.conf['device'])
            logger.info('{}_{} model generated'.format(self.conf['net_mode'], self.conf['net_depth']))

        model_path = os.path.join(os.getcwd(), self.conf['model_path'], f'model_{self.model_name}')
        model.load_state_dict(torch.load(model_path, map_location=torch.device(self.conf['device']), weights_only=True))
        model.eval()
        
        logger.info("FaceNetRecognizer model loaded")
        return model

    def _load_facebank(self):
        """Load facebank data"""
        if os.path.exists(os.path.join(self.conf['facebank_path'], 'facebank.pth')):
            self.embeddings = torch.load(os.path.join(self.conf['facebank_path'], 'facebank.pth'), map_location=self.conf['device'], weights_only=True)
            self.names = np.load(os.path.join(self.conf['facebank_path'], 'names.npy'), allow_pickle=True)
        else:
            self.prepare_facebank()
            
    def update_person_embeddings(self, person_name):
        """Update the embeddings only for a specific person"""
        if not person_name:
            logger.error("No person name provided for update_person_embeddings")
            return False

        # Find all indices where this person appears
        person_indices = []
        if self.names is not None:
            for i, name in enumerate(self.names):
                if name == person_name:
                    person_indices.append(i)

        # Determine the directory to scan based on whether it's an unknown person
        is_unknown = person_name.startswith("Unknown_")
        base_dir = self.conf['facebank_path']
        if is_unknown:
            person_dir = os.path.join(base_dir, 'unknowns', person_name)
        else:
            person_dir = os.path.join(base_dir, person_name)

        # If directory doesn't exist or can't be accessed, remove ALL instances of person from facebank
        if not os.path.exists(person_dir) or not os.access(person_dir, os.R_OK):
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from FaceNet model")
            
            # Save the updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return True

        # Gather all face embeddings for this person
        embs = []
        try:
            for file in os.scandir(person_dir):
                if not file.is_file():
                    continue
                try:
                    img = Image.open(file.path)
                    if img.size != (112, 112):
                        try:
                            img = self.mtcnn.align(img)
                        except Exception as e:
                            logger.error(f"Error aligning image {file.path}: {e}")
                            continue
                    
                    if img is None:
                        logger.error(f"Alignment failed for image {file.path}")
                        continue
                        
                    with torch.no_grad():
                        embs.append(self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)))
                except Exception as e:
                    logger.error(f"Error processing image {file.path}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error scanning directory {person_dir}: {e}")
            return False

        # If no embeddings were found, remove ALL instances of person from facebank
        if len(embs) == 0:
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from FaceNet model (no embeddings found)")
            
            # Save the updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return True

        # Calculate the mean embedding
        embedding = torch.cat(embs).mean(0, keepdim=True)

        # Remove ALL existing instances and add one new instance
        if person_indices:
            # Remove ALL existing instances (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.names) > 1:
                        self.embeddings = self.embeddings[1:]
                        self.names = self.names[1:]
                    else:
                        self.embeddings = None
                        self.names = None
                        break
                else:
                    self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                    self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])

        # Add the new single embedding for this person
        if self.embeddings is None:
            self.embeddings = embedding
            self.names = np.array([person_name])
        else:
            self.embeddings = torch.cat([self.embeddings, embedding])
            self.names = np.append(self.names, person_name)

        logger.info(f"Updated embeddings for {person_name} - consolidated {len(person_indices)} instances into 1")

        # Save the updated facebank
        torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
        return True



    def prepare_facebank(self):
        embeddings = []
        names = []  # Don't include "Unknown" in embeddings, handle separately

        # First scan regular face directories
        for path in os.scandir(self.conf['facebank_path']):
            if path.is_file():
                continue
            
            # Skip unknowns directory, we'll handle it separately
            if path.name == 'unknowns':
                continue
                
            embs = self._process_person_directory(path.path)
            
            if len(embs) == 0:
                print(f"No embeddings found for directory {path.path}")
                continue

            embedding = torch.cat(embs).mean(0, keepdim=True)
            embeddings.append(embedding)
            names.append(path.name)
            print(f'Added {path.name}')

        # Don't include unknowns directory in main facebank anymore 
        # They will be handled by unknown_facebank_manager

        if len(embeddings) == 0:
            # print("No embeddings were created")
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            self.embeddings = None
            self.names = np.array([])  # Empty array when no known persons
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return

        self.embeddings = torch.cat(embeddings)
        self.names = np.array(names)
        torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
        # print(f"Facebank prepared with {len(names)} persons")
        
    def _process_person_directory(self, directory_path):
        """Process a directory of face images and return embeddings"""
        embs = []
        for file in os.scandir(directory_path):
            if not file.is_file():
                continue
                
            try:
                img = Image.open(file.path)
            except Exception as e:
                print(f"Error opening image {file.path}: {e}")
                continue

            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img)
                except Exception as e:
                    print(f"Error aligning image {file.path}: {e}")
                    continue

            if img is None:
                print(f"Alignment failed for image {file.path}")
                continue

            with torch.no_grad():
                try:
                    embs.append(self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)))
                except Exception as e:
                    print(f"Error during model inference for image {file.path}: {e}")
                    continue
                    
        return embs

    def recognize(self, frame, threshold=None, camera_id=None):
        """
        Optimized two-tier face recognition system with cached embeddings:
        1. Generate embeddings once for all faces using infer_batch_process
        2. Check against main face bank (known persons)
        3. Check against unknown face bank (existing unknown persons) using cached embeddings
        4. Create new unknown person if no matches found
        """
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        start_time = time.time()
        bboxes, faces = self.mtcnn.align_multi(image, self.conf['face_limit'], self.conf['min_face_size'])
        end_time = time.time()
        mtccn_delay = end_time - start_time
        writer.add_scalar(f'MTCNN_Delay/Camera_{camera_id}', mtccn_delay, start_time)

        if bboxes is None or len(bboxes) == 0:
            return bboxes, [], []

        # remove the last value of box and extend the box size 1 pixel in each side
        bboxes = bboxes[:, :-1]
        bboxes = bboxes.astype(int)
        bboxes = bboxes + [-1, -1, 1, 1]

        # Step 1: Generate embeddings once for all faces (OPTIMIZED - single embedding calculation)
        infer_start_time = time.time()
        face_embeddings = self._generate_face_embeddings_batch(faces)
        infer_end_time = time.time()
        infer_delay = infer_end_time - infer_start_time
        writer.add_scalar(f'INFER_Delay/Camera_{camera_id}', infer_delay, start_time)

        if face_embeddings is None:
            return bboxes, ["Unknown"] * len(bboxes), [0.0] * len(bboxes)

        # Use configured thresholds
        known_threshold = threshold if threshold is not None else self.conf['known_threshold']

        names = []
        confidence_scores = []

        # Step 2: Check each face against main face bank using cached embeddings
        for i, face_emb in enumerate(face_embeddings):
            face_emb_batch = face_emb.unsqueeze(0)  # Convert to batch format

            # Check against main face bank first (if available)
            if self.embeddings is not None and len(self.embeddings) > 0:
                # Use cached embedding to check main facebank
                results, confidences = self._check_main_facebank_with_embedding(face_emb_batch, known_threshold)
                result_int = results.item() if isinstance(results, torch.Tensor) else int(results)

                if result_int != -1:
                    # Found match in main face bank
                    if 0 <= result_int < len(self.names):
                        person_name = self.names[result_int]
                        names.append(person_name)
                        conf_score = confidences.item() if isinstance(confidences, torch.Tensor) else float(confidences)
                        confidence_scores.append(conf_score)
                        logger.debug(f"Matched known person: {person_name} with confidence {conf_score:.3f}")
                        continue
                    else:
                        logger.warning(f"Index {result_int} out of bounds for self.names with length {len(self.names)}")

            # Step 3: No match in main face bank, check unknown face bank using cached embedding
            unknown_indices, unknown_names_list, unknown_confidences = self._check_unknown_facebank(face_emb_batch)

            if unknown_indices[0] != -1:
                # Found match in unknown face bank
                names.append(unknown_names_list[0])
                confidence_scores.append(unknown_confidences[0])
                logger.debug(f"Matched existing unknown person: {unknown_names_list[0]}")
            else:
                # Step 4: Create new unknown person
                new_unknown_id = self._create_new_unknown_person(
                    faces[i], camera_id=camera_id, full_frame=frame
                )
                if new_unknown_id:
                    names.append(new_unknown_id)
                    confidence_scores.append(1.0)
                    logger.info(f"Created new unknown person: {new_unknown_id}")
                else:
                    names.append("Unknown")
                    confidence_scores.append(0.0)

        return bboxes, names, confidence_scores


    def _generate_face_embeddings_batch(self, faces):
        """
        Generate embeddings for a list of face images using optimized batch processing.
        This replaces the old _generate_face_embeddings but maintains the same interface
        while using the proven infer_batch_process logic.

        Args:
            faces: List of PIL face images

        Returns:
            torch.Tensor: Face embeddings or None if generation failed
        """
        try:
            if len(faces) == 0:
                return None

            # Use the same efficient batch processing as infer_batch_process
            # but only return embeddings without similarity calculation
            batch_tensor = torch.stack([self.conf['test_transform'](img) for img in faces]).to(self.conf['device'])

            # Process entire batch at once - same as infer_batch_process
            with torch.no_grad():
                embeddings = self.model(batch_tensor)

            return embeddings

        except Exception as e:
            logger.error(f"Error generating face embeddings: {str(e)}")
            return None

    def _check_main_facebank_with_embedding(self, face_embedding, threshold):
        """
        Check a single face embedding against the main face bank using cached embedding.
        This replaces the old _check_main_facebank method with optimized logic.

        Args:
            face_embedding: Single face embedding (batch size 1)
            threshold: Distance threshold for matching

        Returns:
            tuple: (result_index, confidence_score)
                - result_index: Index of matched person (-1 for no match)
                - confidence_score: Confidence score (0.0-1.0)
        """
        try:
            if self.embeddings is None or len(self.embeddings) == 0:
                return torch.tensor(-1), torch.tensor(0.0)

            # Calculate distances using the same logic as infer_batch_process
            diff = face_embedding.unsqueeze(-1) - self.embeddings.transpose(1, 0).unsqueeze(0)
            dist = torch.sum(torch.pow(diff, 2), dim=1)
            minimum, min_idx = torch.min(dist, dim=1)

            # Apply threshold
            if minimum.item() > threshold:
                min_idx = torch.tensor(-1)
                confidence = torch.tensor(0.0)
            else:
                # Convert distance to confidence: confidence = 1 - (distance / max_distance)
                confidence = 1.0 - (minimum / threshold)
                confidence = torch.clamp(confidence, 0.0, 1.0)

            return min_idx, confidence

        except Exception as e:
            logger.error(f"Error checking main face bank with embedding: {str(e)}")
            return torch.tensor(-1), torch.tensor(0.0)


        
    def infer_batch_process(self, faces, target_embs, threshold, tta=False):
        """
        Batch processing version of infer function for better performance
        Processes all faces at once instead of using for loop
        
        Args:
            faces: List of face images (PIL Images)
            target_embs: Target embeddings to compare against
            threshold: Distance threshold for recognition
            tta: Test Time Augmentation flag
            
        Returns:
            min_idx: Indices of matched persons (-1 for unknown)
            minimum: Minimum distances for each face
        """
        if len(faces) == 0:
            return torch.tensor([]), torch.tensor([])
        
        # Prepare batch tensor for all faces
        if tta:
            # For TTA, we need to process original and mirrored images - efficient approach
            # Transform and stack original images directly
            batch_tensor = torch.stack([self.conf['test_transform'](img) for img in faces]).to(self.conf['device'])
            # Transform and stack mirrored images directly
            batch_mirror_tensor = torch.stack([
                self.conf['test_transform'](tv_transforms.functional.hflip(img)) for img in faces
            ]).to(self.conf['device'])
            
            # Process both batches at once
            with torch.no_grad():
                embs = self.model(batch_tensor)
                embs_mirror = self.model(batch_mirror_tensor)
                
            # Combine original and mirrored embeddings with L2 normalization
            source_embs = l2_norm(embs + embs_mirror)
        else:
            # Standard batch processing without TTA - more efficient approach
            # Transform all images and stack directly into batch tensor
            batch_tensor = torch.stack([self.conf['test_transform'](img) for img in faces]).to(self.conf['device'])
            
            # Process entire batch at once
            with torch.no_grad():
                source_embs = self.model(batch_tensor)

        # Calculate distances between all source embeddings and target embeddings
        diff = source_embs.unsqueeze(-1) - target_embs.transpose(1, 0).unsqueeze(0)
        dist = torch.sum(torch.pow(diff, 2), dim=1)
        minimum, min_idx = torch.min(dist, dim=1)
        min_idx[minimum > threshold] = -1  # if no match, set idx to -1

        # Convert distance to confidence: confidence = 1 - (distance / max_distance)
        # Use threshold as max_distance for normalization
        confidence = 1.0 - (minimum / threshold)
        confidence = torch.clamp(confidence, 0.0, 1.0)  # Ensure 0-1 range

        return min_idx, confidence



    def _check_unknown_facebank(self, face_embeddings, threshold=None):
        """
        Check face embeddings against unknown face bank for potential matches.

        Args:
            face_embeddings: Face embeddings to check (torch.Tensor)
            threshold: Distance threshold for unknown matching (optional)

        Returns:
            tuple: (matched_indices, matched_names, confidences)
                - matched_indices: List of indices (-1 for no match)
                - matched_names: List of matched unknown person names
                - confidences: List of confidence scores
        """
        if not self.conf['enable_unknown_facebank']:
            # Unknown face bank disabled, return no matches
            num_faces = face_embeddings.shape[0]
            return [-1] * num_faces, ["Unknown"] * num_faces, [0.0] * num_faces

        # Use configured threshold or default
        if threshold is None:
            threshold = self.conf['unknown_threshold']

        # Get unknown face bank embeddings
        unknown_embeddings = self.unknown_manager.embeddings
        unknown_names = self.unknown_manager.names

        if unknown_embeddings is None or len(unknown_names) == 0:
            # No unknown persons in face bank
            num_faces = face_embeddings.shape[0]
            return [-1] * num_faces, ["Unknown"] * num_faces, [0.0] * num_faces

        # Calculate distances between face embeddings and unknown embeddings
        diff = face_embeddings.unsqueeze(-1) - unknown_embeddings.transpose(1, 0).unsqueeze(0)
        dist = torch.sum(torch.pow(diff, 2), dim=1)
        minimum, min_idx = torch.min(dist, dim=1)

        # Apply threshold
        min_idx[minimum > threshold] = -1

        # Convert to confidence scores
        confidence = 1.0 - (minimum / threshold)
        confidence = torch.clamp(confidence, 0.0, 1.0)

        # Build results
        matched_indices = []
        matched_names = []
        confidences = []

        for idx, conf in zip(min_idx, confidence):
            idx_val = idx.item() if isinstance(idx, torch.Tensor) else int(idx)
            conf_val = conf.item() if isinstance(conf, torch.Tensor) else float(conf)

            if idx_val == -1:
                matched_indices.append(-1)
                matched_names.append("Unknown")
                confidences.append(0.0)
            else:
                matched_indices.append(idx_val)
                matched_names.append(unknown_names[idx_val])
                confidences.append(conf_val)
                logger.info(f"Matched unknown person: {unknown_names[idx_val]} with confidence {conf_val:.3f}")

        return matched_indices, matched_names, confidences

    def _create_new_unknown_person(self, face_image, camera_id=None, full_frame=None):
        """
        Create a new unknown person entry in the unknown face bank.

        Args:
            face_image: PIL Image of the detected face
            camera_id: Camera ID for database records (optional)
            full_frame: Full frame image for AlertPhoto (optional)

        Returns:
            str: Generated unknown person ID or None if creation failed
        """
        try:
            # Generate next unknown ID
            unknown_id = self.unknown_manager.get_next_unknown_id()
            logger.info(f"Creating new unknown person with ID: {unknown_id}")

            # First generate the embedding directly from the face_image
            try:
                # Ensure face_image is properly sized and aligned
                img = face_image
                if img.size != (112, 112):
                    try:
                        img = self.mtcnn.align(img, self.conf['min_face_size'])
                    except Exception as e:
                        logger.error(f"Error aligning face for {unknown_id}: {str(e)}")
                        return None

                if img is None:
                    logger.error(f"Face alignment failed for {unknown_id}")
                    return None

                # Generate embedding using our model
                with torch.no_grad():
                    embedding = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
                
                # Add directly to unknown face bank using the embedding
                if self.unknown_manager.embeddings is None:
                    self.unknown_manager.embeddings = embedding
                    self.unknown_manager.names = np.array([unknown_id])
                else:
                    self.unknown_manager.embeddings = torch.cat([self.unknown_manager.embeddings, embedding])
                    self.unknown_manager.names = np.append(self.unknown_manager.names, unknown_id)

                # Save updated unknown face bank
                success = self.unknown_manager._save_unknown_facebank()
                
                if success:
                    # Create directory for this unknown person (use media path like PersonService)
                    from django.conf import settings
                    base_dir = settings.MEDIA_ROOT
                    unknown_person_dir = os.path.join(base_dir, 'alert_photos', 'unknowns', unknown_id)
                    os.makedirs(unknown_person_dir, exist_ok=True)

                    # Create database records
                    try:
                        with transaction.atomic():
                            # Get camera object if camera_id is provided
                            camera = None
                            if camera_id:
                                try:
                                    camera = Camera.objects.get(id=camera_id)
                                except Camera.DoesNotExist:
                                    logger.warning(f"Camera with ID {camera_id} not found")

                            # Create AlertPerson
                            alert_person = AlertPerson.objects.create(
                                user=camera.user if camera else None,
                                name=unknown_id,
                                is_unknown=True,
                                first_seen_camera=camera
                            )

                            # Create AlertPhoto
                            # Use full frame if provided, otherwise use face_image
                            buffer = io.BytesIO()
                            if full_frame is not None:
                                # Convert full frame (numpy array) to bytes
                                _, frame_buffer = cv2.imencode('.jpg', full_frame)
                                content = ContentFile(frame_buffer.tobytes())
                                logger.info("Using full frame for AlertPhoto")
                            else:
                                # Use face_image (existing behavior)
                                face_image.save(buffer, format='JPEG')
                                content = ContentFile(buffer.getvalue())
                                logger.info("Using face image for AlertPhoto")
                            
                            alert_photo = AlertPhoto.objects.create(
                                person=alert_person,
                                is_primary=True
                            )
                            alert_photo.photo.save(
                                f'{unknown_id}.jpg',
                                content,
                                save=True
                            )

                            logger.info(f"Database records created for {unknown_id}: Person ID {alert_person.id}, Photo ID {alert_photo.id}")

                    except Exception as e:
                        logger.error(f"Error creating database records for {unknown_id}: {str(e)}")
                        # Continue anyway - facebank record was successful

                    logger.info(f"Successfully created new unknown person: {unknown_id}")
                    return unknown_id
                else:
                    logger.error(f"Failed to save unknown face bank for {unknown_id}")
                    return None

            except Exception as e:
                logger.error(f"Error generating embedding for {unknown_id}: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"Error creating new unknown person: {str(e)}")
            return None

    def add_face(self, name, filepath):
        try:
            img = Image.open(filepath)
            # Align face if needed
            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img, self.conf['min_face_size'])
                except Exception as e:
                    logger.error(f"Error aligning face for {name}: {str(e)}")
                    return False
            
            if img is None:
                logger.error(f"Face alignment failed for {name}")
                return False
                
            # Generate embedding
            with torch.no_grad():
                emb = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
            
            # Set device consistently
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            emb = emb.to(device)
            
            # Update tensor and names
            if self.embeddings is None or len(self.embeddings) == 0:
                self.embeddings = emb
                self.names = np.array([name])
            else:
                # Ensure embeddings are on the same device as new emb
                self.embeddings = self.embeddings.to(device)
                self.embeddings = torch.cat([self.embeddings, emb])
                self.names = np.append(self.names, name)
            
            # Save updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            
            # Serialize tensor to binary format and save to AlertPhoto
            buffer = io.BytesIO()
            torch.save(emb, buffer)
            buffer.seek(0)
            
            try:
                photo_instance = AlertPhoto.objects.get(photo=filepath)
                photo_instance.image_vector_facenet = buffer.getvalue()
                photo_instance.save()
            except AlertPhoto.DoesNotExist:
                logger.info(f"No AlertPhoto found for {filepath}, continuing without saving vector")
            
            return True
        except Exception as e:
            logger.error(f"Error in add_face: {str(e)}")
            return False

    def detect_faces(self, frame):
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        bboxes, faces = self.mtcnn.align_multi(image, self.conf['face_limit'], self.conf['min_face_size'])
        return bboxes, faces

    def load_tensor(self, data):
        buffer = io.BytesIO(data)
        tensor = torch.load(buffer)
        return tensor