// Zone visibility management for stream viewer
class ZoneVisibilityManager {
    constructor() {
        this.canvas = null;
        this.video = null;
        this.toggleZoneVisibility = null;
        this.canvasManager = null;
        this.polygons = [];
        this.zonesLoaded = false;
        this.cameraId = null;
        this.isStreamViewer = false;
        this.initialized = false;
        
        this.init();
    }

    init() {
        // Check if we're in the stream viewer page (not zone creator)
        this.isStreamViewer = !window.location.pathname.includes('zone_creator');
        
        if (!this.isStreamViewer) {
            console.log("Zone visibility manager: Not in stream viewer, skipping initialization");
            return;
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.performInitialization();
            });
        } else {
            this.performInitialization();
        }
    }

    performInitialization() {
        if (this.initialized) {
            return;
        }

        const success = this.initializeElements();
        if (success) {
            this.initialized = true;
            this.startZoneDisplayInitialization();
        }
    }

    initializeElements() {
        if (!this.isStreamViewer) {
            return false;
        }

        this.canvas = document.getElementById("draw-canvas");
        this.video = document.getElementById("stream");
        this.toggleZoneVisibility = document.getElementById("toggle-zone-visibility");
        
        const cameraIdElement = document.getElementById("camera-id");
        this.cameraId = cameraIdElement ? cameraIdElement.value : null;

        if (!this.canvas) {
            console.log("Zone visibility: Canvas element not found");
            return false;
        }

        if (!this.video) {
            console.log("Zone visibility: Video element not found");
            return false;
        }

        if (!this.toggleZoneVisibility) {
            console.log("Zone visibility: Toggle element not found");
            return false;
        }

        // Check if required classes are available
        if (typeof CanvasManager === 'undefined') {
            console.error("Zone visibility: CanvasManager not found");
            return false;
        }

        if (typeof Polygon === 'undefined') {
            console.error("Zone visibility: Polygon class not found");
            return false;
        }

        // Initialize canvas manager
        try {
            this.canvasManager = new CanvasManager(this.canvas);
            console.log("Zone visibility: Canvas manager initialized successfully");
        } catch (error) {
            console.error("Zone visibility: Failed to initialize canvas manager:", error);
            return false;
        }

        this.setupEventListeners();
        return true;
    }

    setupEventListeners() {
        if (!this.canvas || !this.video || !this.toggleZoneVisibility || !this.canvasManager) {
            return;
        }

        // Add event listeners for video events
        this.video.addEventListener('load', () => this.updateCanvasPosition());
        this.video.addEventListener('loadeddata', () => this.updateCanvasPosition());
        this.video.addEventListener('resize', () => this.updateCanvasPosition());

        // Toggle zone visibility
        this.toggleZoneVisibility.addEventListener("change", () => {
            const isVisible = this.toggleZoneVisibility.checked;
            this.canvasManager.setPolygons(isVisible ? this.polygons : []);
            this.canvasManager.draw();
            console.log("Zone visibility toggled:", isVisible);
        });
    }

    updateCanvasPosition() {
        if (!this.canvasManager || !this.video) {
            return;
        }

        if (this.video.complete && this.video.naturalWidth !== 0) {
            this.canvasManager.resizeToMatch(this.video);
            if (this.zonesLoaded && this.toggleZoneVisibility && this.toggleZoneVisibility.checked) {
                this.canvasManager.draw();
            }
        }
    }

    async fetchZones() {
        if (!this.cameraId) {
            console.log("Zone visibility: No camera ID found, skipping zone fetch");
            return;
        }

        try {
            const response = await fetch(`/videostream/get_zones/?camera_id=${this.cameraId}`);
            const result = await response.json();
            
            if (response.ok) {
                const videoWidth = this.video.clientWidth;
                const videoHeight = this.video.clientHeight;

                this.polygons = result.zones.map(zone => {
                    try {
                        const polygon = new Polygon(zone.color);
                        polygon.name = zone.name;

                        // Scale points back to video dimensions
                        polygon.points = zone.points.map(point => ({
                            x: point.x * videoWidth,
                            y: point.y * videoHeight
                        }));

                        polygon.complete = true;
                        return polygon;
                    } catch (error) {
                        console.error("Error creating polygon:", error);
                        return null;
                    }
                }).filter(polygon => polygon !== null);

                this.zonesLoaded = true;

                // Only show zones if toggle is checked
                if (this.toggleZoneVisibility && this.toggleZoneVisibility.checked) {
                    this.canvasManager.setPolygons(this.polygons);
                    this.canvasManager.draw();
                }

                console.log(`Zone visibility: Loaded ${this.polygons.length} zones for display`);
            } else {
                console.error(`Zone visibility: Error fetching zones: ${result.error}`);
            }
        } catch (error) {
            console.error("Zone visibility: Error fetching zones:", error);
        }
    }

    initZoneDisplay() {
        if (!this.video || !this.canvasManager) {
            return;
        }

        if (this.video.complete && this.video.naturalWidth !== 0) {
            this.updateCanvasPosition();
            this.fetchZones();
        } else {
            // Wait a bit more for video to load
            setTimeout(() => this.initZoneDisplay(), 500);
        }
    }

    startZoneDisplayInitialization() {
        if (!this.initialized) {
            console.log("Zone visibility: Not properly initialized, skipping zone display setup");
            return;
        }

        // Start zone display initialization after a delay
        setTimeout(() => this.initZoneDisplay(), 2000);
    }
}

// Only initialize if we're in the stream viewer page
if (typeof window !== 'undefined' && !window.location.pathname.includes('zone_creator')) {
    // Wait for other scripts to load first
    setTimeout(() => {
        new ZoneVisibilityManager();
    }, 100);
}