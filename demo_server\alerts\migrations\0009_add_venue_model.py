# Generated by Django 5.1.4 on 2025-09-18 09:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0008_alarm_alarm_type_delete_personvisit'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Venue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='<PERSON>kan adı (örn: <PERSON>, 1. Kat, Toplantı Salonu)', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Mekan açıklaması')),
                ('is_active', models.BooleanField(default=True, help_text='Mekan aktif mi?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venues', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='alerts_venu_user_id_f8a3b9_idx')],
            },
        ),
    ]
