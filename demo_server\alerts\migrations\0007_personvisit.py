# Generated by Django 5.1.4 on 2025-09-16 08:17

import alerts.models
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0006_remove_alertphoto_image_vector_arcface'),
        ('cameras', '0003_camera_camera_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='PersonVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_type', models.CharField(choices=[('ENTRY', 'Entry'), ('EXIT', 'Exit')], help_text='Type of visit - entry or exit', max_length=10)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('confidence', models.FloatField(default=0.0, help_text='Face recognition confidence score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('video_snapshot', models.FileField(blank=True, help_text='Video frame snapshot of the visit', null=True, upload_to=alerts.models.unique_file_path)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='visits', to='cameras.camera')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='visits', to='alerts.alertperson')),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['person', 'camera', '-timestamp'], name='alerts_pers_person__3a07f9_idx'), models.Index(fields=['person', 'visit_type', '-timestamp'], name='alerts_pers_person__b46477_idx'), models.Index(fields=['camera', 'visit_type', '-timestamp'], name='alerts_pers_camera__6eeef0_idx')],
            },
        ),
    ]
