import os
from django.apps import AppConfig
import signal
import sys
import logging
import time

logger = logging.getLogger(__name__)

class VideostreamConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "videostream"

    def ready(self):
        """Called when Django starts up"""

        def signal_handler(signum, frame):
            """Handle shutdown signals gracefully"""
            
            # Only register signal handlers in the main process, not in worker processes
            if os.environ.get("IS_CAMERA_WORKER") == "1":
                logger.info("Worker process detected, skipping signal handler registration")
                return

            from videostream.managers.__init__ import process_camera_manager
            
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            try:
                # Stop all camera streams
                process_camera_manager.cleanup_all()
                logger.info("All camera streams stopped successfully (apps.py).")
                
            except Exception as e:
                logger.error(f"Error stopping camera streams: {e}")

            # Exit gracefully
            time.sleep(2)  # Allow time for cleanup
            sys.exit(0)
        # Register signal handlers
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

        logger.info("Signal handlers registered for graceful shutdown")