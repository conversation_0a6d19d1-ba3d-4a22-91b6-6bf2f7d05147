// Toast message management system
class ToastManager {
    constructor() {
        this.toastContainer = null;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeElements();
            this.setupExistingToasts();
        });
    }

    initializeElements() {
        this.toastContainer = document.getElementById('toast-container');
        if (!this.toastContainer) {
            console.warn('Toast container not found');
        }
    }

    setupExistingToasts() {
        // Auto-hide existing toasts after 5 seconds
        const toasts = document.querySelectorAll('.toast');
        toasts.forEach(toast => {
            const bsToast = new bootstrap.Toast(toast);
            if (toast.classList.contains('show')) {
                setTimeout(() => {
                    bsToast.hide();
                }, 5000);
            }
        });
    }

    showMessage(message, tags) {
        if (!this.toastContainer) {
            console.warn('Toast container not available');
            return;
        }

        const toastElement = document.createElement('div');
        toastElement.className = 'toast';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'assertive');
        toastElement.setAttribute('aria-atomic', 'true');
        
        let iconClass = 'info-circle-fill';
        let tagLabel = 'Info';
        
        if (tags === 'success') {
            iconClass = 'check-circle-fill';
            tagLabel = 'Success';
        } else if (tags === 'error') {
            iconClass = 'exclamation-triangle-fill';
            tagLabel = 'Error';
        } else if (tags === 'warning') {
            iconClass = 'exclamation-circle-fill';
            tagLabel = 'Warning';
        }
        
        toastElement.innerHTML = `
            <div class="toast-header bg-${tags} text-white border-0">
                <i class="bi bi-${iconClass} me-2"></i>
                <strong class="me-auto">${tagLabel}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        this.toastContainer.appendChild(toastElement);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // Auto remove after hiding
        toastElement.addEventListener('hidden.bs.toast', () => {
            this.toastContainer.removeChild(toastElement);
        });
    }

    showToast(message, type = 'success', duration = 5000) {
        if (!this.toastContainer) {
            console.warn('Toast container not available');
            return;
        }

        const toastId = 'toast-' + Date.now();
        
        const toastHTML = `
            <div class="toast align-items-center text-bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-${type === 'success' ? 'check-circle-fill' : type === 'danger' ? 'exclamation-triangle-fill' : 'info-circle-fill'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        this.toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Initialize toast manager and make it globally available
const toastManager = new ToastManager();

// Global functions for backward compatibility
window.showMessage = (message, tags) => toastManager.showMessage(message, tags);
window.showToast = (message, type, duration) => toastManager.showToast(message, type, duration);