import os
import logging
import threading
from django.conf import settings

# Import our new service layer
from videostream.services.face_processing_service import FaceProcessingService

logger = logging.getLogger(__name__)

# Singleton FaceRecognitionProcessor sınıfı
class FaceRecognitionProcessor:
    _instance = None
    _init_lock = threading.Lock()

    def __new__(cls, camera_id=None):
        with cls._init_lock:
            if cls._instance is None:
                logger.info(f"Creating new FaceRecognitionProcessor instance")
                cls._instance = super(FaceRecognitionProcessor, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, camera_id=None):
        if camera_id is None:
            raise ValueError("camera_id is required for FaceRecognitionProcessor")

        # Eğer bu camera ID için daha önce init edilmişse, tekrar init etme
        if hasattr(self, '_initialized') and self._initialized:
            logger.info(f"FaceRecognitionProcessor already initialized")
            return

        # İlk kez init ediliyorsa tam init yap
        logger.info(f"Initializing FaceRecognitionProcessor for camera ID: {camera_id}")
        self.camera_id = camera_id

        # Initialize the face processing service
        self.face_processing_service = FaceProcessingService(camera_id)

        # Create necessary directories
        self.base_dir = settings.MEDIA_ROOT
        self.unknown_dir = os.path.join(self.base_dir, 'alert_photos', 'unknowns')
        self.known_dir = os.path.join(self.base_dir, 'alert_photos')

        os.makedirs(self.unknown_dir, exist_ok=True)
        os.makedirs(self.known_dir, exist_ok=True)

        self._initialized = True
        logger.info(f"FaceRecognitionProcessor successfully initialized for camera ID: {camera_id}")

    @classmethod
    def cleanup(cls):
        """Cleanup processor instance"""
        with cls._init_lock:
            if cls._instance is not None:
                # Cleanup service layer
                FaceProcessingService.cleanup()
                cls._instance = None
                logger.info(f"Cleaned up FaceRecognitionProcessor instance")

    async def process_frame(self, frame, camera, filter_by_zone=False, zones=None):
        """
        Process a frame with face recognition - delegates to service layer

        Args:
            frame: Input video frame
            camera: Camera object
            filter_by_zone: Whether to apply zone filtering
            zones: List of zone definitions

        Returns:
            Processed frame with annotations
        """
        try:
            return await self.face_processing_service.process_frame(
                frame, camera, filter_by_zone, zones
            )
        except Exception as e:
            logger.error(f"Error in face recognition: {str(e)}")
            return frame


