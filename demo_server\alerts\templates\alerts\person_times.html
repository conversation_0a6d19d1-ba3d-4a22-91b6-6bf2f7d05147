{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'alerts/css/person_times.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header with Person Info and Photo -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="person-photo">
                {% if person.is_unknown %}
                    <!-- Unknown person - use DB photo if available -->
                    {% if person.photos.exists %}
                        <img src="{{ person.photos.first.photo.url }}" 
                             alt="{{ person.name }}" 
                             class="img-thumbnail person-avatar">
                    {% else %}
                        <div class="photo-placeholder unknown">
                            <i class="bi bi-person-question"></i>
                            <small>Unknown</small>
                        </div>
                    {% endif %}
                {% else %}
                    <!-- Known person - use API photo if available -->
                    {% if employee_photo %}
                        <img src="data:image/jpeg;base64,{{ employee_photo }}" 
                             alt="{{ person.name }}" 
                             class="img-thumbnail person-avatar"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="photo-placeholder known api-error" style="display: none;">
                            <i class="bi bi-person-x"></i>
                            <small>Photo Error</small>
                        </div>
                    {% else %}
                        <div class="photo-placeholder known">
                            <i class="bi bi-person-check"></i>
                            <small>{% if person.employee_id %}ID: {{ person.employee_id }}{% else %}Known{% endif %}</small>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-8">
            <h2>{{ person.name }} - Entry/Exit Times</h2>
            <p class="text-muted">Showing data for {{ selected_date|date:"F d, Y" }}</p>
            {% if person.is_unknown %}
                <span class="badge bg-warning">Unknown Person</span>
            {% else %}
                <span class="badge bg-success">Known Person</span>
            {% endif %}
        </div>
        <div class="col-md-2 text-end">
            <a href="{% url 'alerts:person_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Persons
            </a>
        </div>
    </div>

    <!-- Date Selection -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Select Date</h5>
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <input type="date" 
                           name="date" 
                           value="{{ selected_date|date:'Y-m-d' }}" 
                           class="form-control"
                           onchange="this.form.submit()">
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?date={% now 'Y-m-d' %}" class="btn btn-outline-primary btn-sm">Today</a>
                        <a href="?date={% now 'Y-m-d'|add:'-1 day' %}" class="btn btn-outline-primary btn-sm">Yesterday</a>
                        <a href="?date={% now 'Y-m-d'|add:'-7 days' %}" class="btn btn-outline-primary btn-sm">Week Ago</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Excel-like Table -->
    {% if has_data %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-table"></i> Entry/Exit Records by Location
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-bordered mb-0 excel-table">
                        <thead class="table-dark">
                            <tr>
                                <th class="location-col">Location</th>
                                <th class="time-col">Entry Time</th>
                                <th class="time-col">Exit Time</th>
                                <th class="duration-col">Duration</th>
                                <th class="camera-col">Entry Camera</th>
                                <th class="camera-col">Exit Camera</th>
                                <th class="confidence-col">Entry Confidence</th>
                                <th class="confidence-col">Exit Confidence</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for venue_id, data in venue_summary.items %}
                                {% for pair in data.entry_exit_pairs %}
                                <tr class="venue-row">
                                    {% if forloop.first %}
                                        <td class="venue-name" rowspan="{{ data.entry_exit_pairs|length }}">
                                            <div class="venue-info">
                                                <i class="bi bi-building"></i>
                                                <strong>{{ data.venue_name }}</strong>
                                                <small class="text-muted d-block">{{ data.total_events }} events</small>
                                            </div>
                                        </td>
                                    {% endif %}
                                    
                                    <!-- Entry Time -->
                                    <td class="entry-time">
                                        {% if pair.entry %}
                                            <span class="time-value entry">{{ pair.entry.date|time:"H:i:s" }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Exit Time -->
                                    <td class="exit-time">
                                        {% if pair.exit %}
                                            <span class="time-value exit">{{ pair.exit.date|time:"H:i:s" }}</span>
                                        {% else %}
                                            <span class="text-warning">Still Inside</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Duration -->
                                    <td class="duration">
                                        {% if pair.duration %}
                                            <span class="duration-value">
                                                {% if pair.hours %}{{ pair.hours }}h {% endif %}{{ pair.minutes }}m
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Entry Camera -->
                                    <td class="camera-name">
                                        {% if pair.entry_camera %}
                                            <span class="camera-badge entry">{{ pair.entry_camera.name }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Exit Camera -->
                                    <td class="camera-name">
                                        {% if pair.exit_camera %}
                                            <span class="camera-badge exit">{{ pair.exit_camera.name }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Entry Confidence -->
                                    <td class="confidence">
                                        {% if pair.entry %}
                                            <span class="confidence-badge {% if pair.entry.confidence > 0.7 %}high{% elif pair.entry.confidence > 0.5 %}medium{% else %}low{% endif %}">
                                                {{ pair.entry.confidence_percentage }}
                                            </span>
                                            {% if pair.entry.is_auto_generated %}
                                                <span class="text-danger ms-1" 
                                                      title="{% if pair.entry.auto_generation_reason == 'MISSED_EXIT_AUTO' %}Otomatik exit (missed exit){% elif pair.entry.auto_generation_reason == 'MISSED_EXIT_REENTRY' %}Otomatik entry (exit kaçırıldı){% else %}Otomatik alarm{% endif %}"
                                                      data-bs-toggle="tooltip">
                                                    <i class="bi bi-exclamation-triangle-fill"></i>
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Exit Confidence -->
                                    <td class="confidence">
                                        {% if pair.exit %}
                                            <span class="confidence-badge {% if pair.exit.confidence > 0.7 %}high{% elif pair.exit.confidence > 0.5 %}medium{% else %}low{% endif %}">
                                                {{ pair.exit.confidence_percentage }}
                                            </span>
                                            {% if pair.exit.is_auto_generated %}
                                                <span class="text-danger ms-1" 
                                                      title="{% if pair.exit.auto_generation_reason == 'MISSED_EXIT_AUTO' %}Otomatik exit (missed exit){% elif pair.exit.auto_generation_reason == 'MISSED_EXIT_REENTRY' %}Otomatik entry (exit kaçırıldı){% else %}Otomatik alarm{% endif %}"
                                                      data-bs-toggle="tooltip">
                                                    <i class="bi bi-exclamation-triangle-fill"></i>
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-info text-center" role="alert">
            <i class="bi bi-info-circle"></i>
            <h4>No Records Found</h4>
            <p>No entry/exit records found for <strong>{{ person.name }}</strong> on {{ selected_date|date:"F d, Y" }}.</p>
            <hr>
            <p class="mb-0">Try selecting a different date or check if the person has been detected by any camera.</p>
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltip'leri aktifleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}