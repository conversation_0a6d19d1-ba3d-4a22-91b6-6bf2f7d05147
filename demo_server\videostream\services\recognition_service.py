import logging
from asgiref.sync import sync_to_async
from recognition.model_manager import ModelManager

logger = logging.getLogger(__name__)


class RecognitionService:
    """Service for handling face recognition operations"""
    
    _model_manager = None
    _recognizer = None
    
    @classmethod
    def get_recognizer(cls, camera_id=None):
        """Get or create recognizer instance"""
        if cls._model_manager is None:
            cls._model_manager = ModelManager()
        
        if cls._recognizer is None:
            cls._recognizer = cls._model_manager.get_model("facenet")
            if cls._recognizer:
                logger.info(f"Recognizer initialized for camera {camera_id}")
            else:
                logger.error(f"Could not initialize recognizer for camera {camera_id}")
        
        return cls._recognizer
    
    @classmethod
    async def detect_faces(cls, frame, camera_id):
        """
        Perform face detection and recognition on frame
        
        Args:
            frame: Input video frame
            camera_id: Camera identifier
            
        Returns:
            tuple: (bboxes, names, confidences) or (None, None, None) if no faces
        """
        try:
            recognizer = cls.get_recognizer(camera_id)
            if not recognizer:
                logger.error(f"No recognizer available for camera {camera_id}")
                return None, None, None
            
            # Perform recognition
            bboxes, names, confidences = await sync_to_async(recognizer.recognize)(
                frame, camera_id=camera_id
            )
            
            # Validate results
            if bboxes is None or len(bboxes) == 0:
                return None, None, None
                
            logger.debug(f"Detected {len(names)} faces on camera {camera_id}")
            return bboxes, names, confidences
            
        except Exception as e:
            logger.error(f"Error in face recognition for camera {camera_id}: {str(e)}")
            return None, None, None
    
    @classmethod
    def cleanup(cls):
        """Cleanup recognizer resources"""
        if cls._model_manager:
            cls._model_manager.cleanup()
            cls._model_manager = None
            cls._recognizer = None
            logger.info("Recognition service cleaned up")
