// Enable tooltips and initialize camera status checking
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Start real-time status checking
    checkCameraStatuses();
    setInterval(checkCameraStatuses, 5000); // Check every 5 seconds
});

function checkCameraStatuses() {
    // Get the check camera status URL from a data attribute or global variable
    const checkStatusUrl = window.cameraStatusUrl || '/cameras/check_status/';
    
    fetch(checkStatusUrl)
        .then(response => response.json())
        .then(data => {
            const statuses = data.statuses;

            for (const [cameraId, status] of Object.entries(statuses)) {
                updateCameraStatus(cameraId, status);
            }
        })
        .catch(error => {
            console.error('Error checking camera statuses:', error);
        });
}

function updateCameraStatus(cameraId, status) {
    const statusElement = document.getElementById(`status-${cameraId}`);
    if (!statusElement) return;

    const statusText = statusElement.querySelector('.status-text');
    const icon = statusElement.querySelector('i');

    // Remove all existing classes
    statusElement.className = 'badge';

    if (status === 'streaming') {
        statusElement.classList.add('bg-success');
        icon.className = 'bi bi-play-circle-fill me-1';
        statusText.textContent = 'Streaming';
    } else if (status === 'starting') {
        statusElement.classList.add('bg-info');
        icon.className = 'bi bi-hourglass-split me-1';
        statusText.textContent = 'Starting...';
    } else if (status === 'timeout') {
        statusElement.classList.add('bg-warning');
        icon.className = 'bi bi-exclamation-triangle-fill me-1';
        statusText.textContent = 'Timeout';
    } else if (status === 'error') {
        statusElement.classList.add('bg-danger');
        icon.className = 'bi bi-x-circle-fill me-1';
        statusText.textContent = 'Error';
    } else {
        statusElement.classList.add('bg-secondary');
        icon.className = 'bi bi-pause-circle-fill me-1';
        statusText.textContent = 'Not Streaming';
    }
}