# WireMock Test Setup for Employee Photo API

## 🎯 <PERSON><PERSON><PERSON><PERSON>!

**✅ <PERSON><PERSON><PERSON><PERSON>:**
- WireMock mapping files oluşturuldu
- Base64 test fotoğrafları generate edildi  
- Django settings güncellendi (localhost:8080)
- Test data oluşturuldu (5 known person + employee IDs)

## 🚀 WireMock Kurulum & Test

### 1. WireMock JAR İndirin:
<PERSON> o<PERSON> indirin: [WireMock JAR](https://repo1.maven.org/maven2/com/github/tomakehurst/wiremock-jre8-standalone/2.35.0/wiremock-jre8-standalone-2.35.0.jar)

`wiremock/` dizinine `wiremock-standalone.jar` olarak kaydedin.

### 2. WireMock Başlatın:
```bash
cd wiremock
java -jar wiremock-standalone.jar --port 8080
```

**Expected Output:**
```
|  /\/\  (_)_ __ ___   /\/\   ___   ___| | __
| |    | | '__/ _ \ |    \ / _ \ / __| |/ /
| | /\ | | | |  __/ | /\  | (_) | (__|   <
|_|  \_\_|_|  \___| |_|  \_\___/ \___|_|\_\

port:                         8080
```

### 3. API Test Edin:
```bash
curl http://localhost:8080/GetUserData
```

**Expected Response:**
```json
[
  {
    "photodata": "iVBORw0KGgoAAAANSUhEUgAA...",
    "employeeId": "1001"
  },
  ...
]
```

## 🧪 Django Test

### 1. Django Server Başlatın:
```bash
cd demo_server
python manage.py runserver
```

### 2. Test Endpoints:
- **API Test**: http://localhost:8000/alerts/test-employee-api/
- **Specific Employee**: http://localhost:8000/alerts/test-employee-api/?name=1001
- **Person Times**: http://localhost:8000/alerts/person/{person_id}/times/

## 📊 Test Data

**Known Persons (with Employee IDs):**
- Burak (ID: 1001)
- Ali (ID: 1002) 
- Ayşe (ID: 1003)
- Mehmet (ID: 1004)
- Fatma (ID: 1005)

**Unknown Persons:**
- Unknown_1, Unknown_2, etc.

## 🔄 Full Test Flow

1. **Start WireMock** (port 8080)
2. **Start Django** (port 8000)
3. **Visit person times page** for a known person
4. **Verify photo shows up** from API
5. **Check logs** for API calls

## 🛠️ Directory Structure:
```
wiremock/
├── mappings/
│   └── employee-api.json        ✅ Created
├── __files/
│   └── employee-data.json       ✅ Generated with photos  
└── wiremock-standalone.jar      ⏳ Download manually
```