# Generated by Django 5.1.4 on 2025-05-29 09:18

import alerts.models
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cameras', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AlertPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('is_unknown', models.BooleanField(default=False)),
                ('last_seen_date', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_seen_camera', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='cameras.camera')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alert_persons', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AlertPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo', models.ImageField(upload_to=alerts.models.unique_file_path)),
                ('image_vector_facenet', models.BinaryField(blank=True, null=True)),
                ('image_vector_arcface', models.BinaryField(blank=True, null=True)),
                ('is_primary', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_multiple_face', models.BooleanField(default=False)),
                ('person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='alerts.alertperson')),
            ],
        ),
        migrations.CreateModel(
            name='Alarm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('video_snapshot', models.FileField(blank=True, null=True, upload_to=alerts.models.unique_file_path)),
                ('confidence', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cameras.camera')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='alerts.alertperson')),
                ('alert_photo', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='alerts.alertphoto')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.AddIndex(
            model_name='alertperson',
            index=models.Index(fields=['name', 'is_unknown'], name='alerts_aler_name_d35c4a_idx'),
        ),
        migrations.AddIndex(
            model_name='alertphoto',
            index=models.Index(fields=['person', 'is_primary'], name='alerts_aler_person__a9fe92_idx'),
        ),
        migrations.AddIndex(
            model_name='alarm',
            index=models.Index(fields=['person', '-date'], name='alerts_alar_person__b778b5_idx'),
        ),
    ]
