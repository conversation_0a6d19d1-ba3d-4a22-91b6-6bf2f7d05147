import os

# Import multiprocessing utilities for CUDA compatibility
from .mp_utils import setup_multiprocessing

from django.core.asgi import get_asgi_application
# from channels.routing import ProtocolTypeRouter, URLRouter
# from channels.auth import AuthMiddlewareStack
# import alerts.routing  # Ensure this import matches your app structure

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "web_server.settings")
print("web server ASGI application is being called")  # Debugging line

# Simple ASGI application without WebSocket support
application = get_asgi_application()

# application = ProtocolTypeRouter({
#    "http": get_asgi_application(),  # Handle HTTP requests
#    "websocket": AuthMiddlewareStack(  # Handle WebSocket connections
#        URLRouter(
#            alerts.routing.websocket_urlpatterns  # Ensure this points to your WebSocket routing
#        )
#    ),
#})

print("asgi")
