from django import forms
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Venue
from cameras.models import Camera


class AlertPersonForm(forms.ModelForm):
    class Meta:
        model = AlertPerson
        fields = ['name']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'})
        }

class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True

class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result

class AlertPhotoForm(forms.Form):
    photos = MultipleFileField(required=True)

class RenameUnknownPersonForm(forms.Form):
    RENAME_CHOICES = [
        ('new', 'Create New Person'),
        ('existing', 'Select Existing Person'),
    ]
    
    rename_type = forms.ChoiceField(
        choices=RENAME_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial='new',
        label="Rename Options"
    )
    
    new_name = forms.CharField(
        max_length=100,
        label="New Person Name",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter new person name'}),
        required=False
    )
    
    existing_person = forms.ModelChoiceField(
        queryset=None,  # Will be set in __init__
        label="Select Existing Person",
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False,
        empty_label="Choose an existing person..."
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            # Only show known persons (not unknown) for the current user
            self.fields['existing_person'].queryset = AlertPerson.objects.filter(
                user=user, 
                is_unknown=False
            ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        rename_type = cleaned_data.get('rename_type')
        new_name = cleaned_data.get('new_name')
        existing_person = cleaned_data.get('existing_person')
        
        if rename_type == 'new' and not new_name:
            raise forms.ValidationError("Please enter a name for the new person.")
        
        if rename_type == 'existing' and not existing_person:
            raise forms.ValidationError("Please select an existing person.")
        
        return cleaned_data


class AlertFilterForm(forms.Form):
    """Form for filtering alerts by various criteria"""
    
    venue = forms.ModelChoiceField(
        queryset=None,  # Will be set in __init__
        required=False,
        empty_label="All Locations",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    person_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter person name...'
        })
    )
    
    alarm_type = forms.ChoiceField(
        choices=[('', 'All Types'), ('ENTRY', 'Entry'), ('EXIT', 'Exit')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    person_type = forms.ChoiceField(
        choices=[('', 'All Persons'), ('known', 'Known Persons'), ('unknown', 'Unknown Persons')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            # Only show venues for the current user
            self.fields['venue'].queryset = Venue.objects.filter(
                user=user, 
                is_active=True
            ).order_by('name')