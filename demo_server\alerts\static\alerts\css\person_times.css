/* Person Times Page Styles */

/* Person Photo Styles */
.person-photo {
    text-align: center;
}

.person-avatar {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}

.photo-placeholder {
    width: 120px;
    height: 120px;
    border: 2px dashed #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.photo-placeholder.known {
    border-color: #28a745;
    background: #d4edda;
    color: #155724;
}

.photo-placeholder.unknown {
    border-color: #ffc107;
    background: #fff3cd;
    color: #856404;
}

.photo-placeholder.api-error {
    border-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

.photo-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

/* Excel Table Styles */
.excel-table {
    font-size: 0.9rem;
    border: 2px solid #dee2e6;
}

.excel-table th {
    background-color: #343a40 !important;
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #495057;
}

.excel-table td {
    vertical-align: middle;
    text-align: center;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
}

/* Column Widths */
.location-col { width: 20%; }
.time-col { width: 12%; }
.duration-col { width: 10%; }
.camera-col { width: 12%; }
.confidence-col { width: 10%; }

/* Venue Name Cell */
.venue-name {
    background-color: #f8f9fa;
    border-right: 3px solid #007bff;
    vertical-align: middle;
    text-align: left;
    padding: 15px 12px;
}

.venue-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.venue-info i {
    margin-right: 0.5rem;
}

/* Time Values */
.time-value.entry {
    color: #28a745;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.time-value.exit {
    color: #dc3545;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* Duration */
.duration-value {
    color: #17a2b8;
    font-weight: 600;
}

/* Camera Badges */
.camera-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.camera-badge.entry {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.camera-badge.exit {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Confidence Badges */
.confidence-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.confidence-badge.high {
    background-color: #28a745;
}

.confidence-badge.medium {
    background-color: #ffc107;
    color: #212529;
}

.confidence-badge.low {
    background-color: #dc3545;
}

/* Row Hover Effects */
.venue-row:hover {
    background-color: #f5f5f5;
}

/* Card Styles */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .person-avatar, .photo-placeholder {
        width: 80px;
        height: 80px;
    }
    
    .excel-table {
        font-size: 0.8rem;
    }
    
    .excel-table td {
        padding: 8px 4px;
    }
}