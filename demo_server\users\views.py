import logging
from django.http import JsonResponse, HttpResponseRedirect
from django.contrib.auth.models import User
from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required, permission_required
from django.urls import reverse
from django.conf import settings
from django.contrib.auth import logout as django_logout
from urllib.parse import urlencode

logger = logging.getLogger(__name__)


@login_required
@permission_required('auth.view_user', raise_exception=True)
def get_user_permissions(request, user_id):
    """Get user permissions as JSO<PERSON> (only for staff/admins)"""
    try:
        user = User.objects.get(id=user_id)

        # Direkt olarak Django'nun kendi permissions sistemini döndürüyoruz
        user_permissions = list(user.get_user_permissions())
        group_permissions = list(user.get_group_permissions())
        roles = list(user.groups.values_list('name', flat=True))

        return JsonResponse({
            'user_permissions': user_permissions,
            'group_permissions': group_permissions,
            'roles': roles
        })

    except User.DoesNotExist:
        return JsonResponse({'error': 'User not found'}, status=404)


def keycloak_logout_callback(request):
    """Handle logout callback from Keycloak"""
    messages.success(request, 'You have been successfully logged out.')
    return redirect('index')


def keycloak_logout(request):
    """Perform logout from Django and Keycloak"""
    id_token = request.session.get('oidc_id_token')

    # Django logout
    django_logout(request)

    # Redirect after Keycloak logout
    post_logout_redirect_uri = request.build_absolute_uri(
        reverse('users:keycloak_logout_callback')
    )

    end_session_url = (
        f"{settings.KEYCLOAK_SERVER_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/logout"
    )

    params = {'post_logout_redirect_uri': post_logout_redirect_uri}
    if id_token:
        params['id_token_hint'] = id_token

    return HttpResponseRedirect(f"{end_session_url}?{urlencode(params)}")