{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2><i class="bi bi-building"></i> <PERSON><PERSON></h2>
            <p class="text-muted">Kameralarınızın atanacağı mekanları yönetin</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'alerts:venue_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i> <PERSON><PERSON>
            </a>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    {% if venues %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Mekanlarınız ({{ venues.count }})</h5>
            </div>
            <div class="card-body">
                <form method="post" id="venueForm">
                    {% csrf_token %}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button type="button" id="selectAll" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-check-square"></i> Tümünü Seç
                            </button>
                            <button type="button" id="deselectAll" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-square"></i> Tümünü Kaldır
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="submit" class="btn btn-danger btn-sm" id="deleteSelected" disabled>
                                <i class="bi bi-trash"></i> Seçilenleri Sil
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="masterCheckbox">
                                    </th>
                                    <th>Mekan Adı</th>
                                    <th>Açıklama</th>
                                    <th>Kamera Sayısı</th>
                                    <th>Durum</th>
                                    <th>Oluşturulma</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for venue in venues %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="venue_ids" value="{{ venue.id }}" class="venue-checkbox">
                                    </td>
                                    <td>
                                        <strong>{{ venue.name }}</strong>
                                    </td>
                                    <td>
                                        {% if venue.description %}
                                            {{ venue.description|truncatewords:10 }}
                                        {% else %}
                                            <span class="text-muted">Açıklama yok</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ venue.cameras.count }} kamera</span>
                                    </td>
                                    <td>
                                        {% if venue.is_active %}
                                            <span class="badge bg-success">Aktif</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Pasif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ venue.created_at|date:"d.m.Y H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'alerts:venue_edit' venue.id %}" class="btn btn-outline-primary" title="Düzenle">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="{% url 'alerts:venue_delete' venue.id %}" class="btn btn-outline-danger" title="Sil">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-building display-1 text-muted"></i>
            <h4 class="mt-3">Henüz hiç mekan eklememişsiniz</h4>
            <p class="text-muted">Kameralarınızı gruplandırmak için mekan eklemeye başlayın</p>
            <a href="{% url 'alerts:venue_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i> İlk Mekanınızı Ekleyin
            </a>
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const masterCheckbox = document.getElementById('masterCheckbox');
    const venueCheckboxes = document.querySelectorAll('.venue-checkbox');
    const deleteButton = document.getElementById('deleteSelected');
    const selectAllBtn = document.getElementById('selectAll');
    const deselectAllBtn = document.getElementById('deselectAll');

    function updateDeleteButton() {
        const checkedBoxes = document.querySelectorAll('.venue-checkbox:checked');
        deleteButton.disabled = checkedBoxes.length === 0;
    }

    function updateMasterCheckbox() {
        const totalBoxes = venueCheckboxes.length;
        const checkedBoxes = document.querySelectorAll('.venue-checkbox:checked').length;
        
        masterCheckbox.indeterminate = checkedBoxes > 0 && checkedBoxes < totalBoxes;
        masterCheckbox.checked = checkedBoxes === totalBoxes && totalBoxes > 0;
    }

    // Master checkbox handler
    masterCheckbox.addEventListener('change', function() {
        venueCheckboxes.forEach(checkbox => {
            checkbox.checked = masterCheckbox.checked;
        });
        updateDeleteButton();
    });

    // Individual checkbox handlers
    venueCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateMasterCheckbox();
            updateDeleteButton();
        });
    });

    // Select/Deselect all buttons
    selectAllBtn.addEventListener('click', function() {
        venueCheckboxes.forEach(checkbox => checkbox.checked = true);
        updateMasterCheckbox();
        updateDeleteButton();
    });

    deselectAllBtn.addEventListener('click', function() {
        venueCheckboxes.forEach(checkbox => checkbox.checked = false);
        updateMasterCheckbox();
        updateDeleteButton();
    });

    // Delete confirmation
    document.getElementById('venueForm').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.venue-checkbox:checked');
        if (checkedBoxes.length > 0) {
            if (!confirm(`${checkedBoxes.length} mekanı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`)) {
                e.preventDefault();
            }
        }
    });
});
</script>
{% endblock %}