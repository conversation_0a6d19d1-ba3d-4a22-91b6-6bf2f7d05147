{% extends 'base.html' %}

{% block content %}
<div class="alert-listing-container">
    <h2>Alerts</h2>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Search & Filter Alerts</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ filter_form.venue.id_for_label }}" class="form-label">Location:</label>
                    {{ filter_form.venue }}
                </div>
                <div class="col-md-3">
                    <label for="{{ filter_form.person_name.id_for_label }}" class="form-label">Person Name:</label>
                    {{ filter_form.person_name }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.alarm_type.id_for_label }}" class="form-label">Alarm Type:</label>
                    {{ filter_form.alarm_type }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.person_type.id_for_label }}" class="form-label">Person Type:</label>
                    {{ filter_form.person_type }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.date_from.id_for_label }}" class="form-label">From Date:</label>
                    {{ filter_form.date_from }}
                </div>
                <div class="col-md-2">
                    <label for="{{ filter_form.date_to.id_for_label }}" class="form-label">To Date:</label>
                    {{ filter_form.date_to }}
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">Search</button>
                        <a href="{% url 'alerts:alert_list' %}" class="btn btn-secondary">Clear</a>
                    </div>
                </div>
            </form>
            {% if total_alerts %}
                <div class="mt-2">
                    <small class="text-muted">Found {{ total_alerts }} alert{{ total_alerts|pluralize }}</small>
                </div>
            {% endif %}
        </div>
    </div>

    <form method="post" action="{% url 'alerts:delete_multiple_alerts' %}">
        {% csrf_token %}

        <div class="mb-3">
            <button type="submit" name="action" value="delete_selected" class="btn btn-warning" onclick="return confirm('Are you sure you want to delete selected alerts?')">Delete Selected</button>
            <button type="submit" name="action" value="delete_all" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete ALL alerts?')">Delete All</button>
        </div>

        <table class="table table-striped">
            <thead>
                <tr>
                    <th><input type="checkbox" onclick="document.querySelectorAll('.alert-checkbox').forEach(cb => cb.checked = this.checked)"></th>
                    <th>Date/Time</th>
                    <th>Camera</th>
                    <th>Location</th>
                    <th>Person</th>
                    <th>Alarm Type</th>
                    <th>Snapshot</th>
                </tr>
            </thead>
            <tbody>
                {% for alarm in page_obj %}
                <tr>
                    <td><input type="checkbox" name="selected_alerts" value="{{ alarm.id }}" class="alert-checkbox"></td>
                    <td>{{ alarm.date|date:"d.m.Y H:i" }}</td>
                    <td>{{ alarm.camera.name }}</td>
                    <td>
                        {% if alarm.venue %}
                            {{ alarm.venue.name }}
                        {% elif alarm.camera.venue %}
                            {{ alarm.camera.venue.name }}
                        {% else %}
                            <span class="text-muted">No Location</span>
                        {% endif %}
                    </td>
                    <td>
                        <span class="{% if alarm.person.is_unknown %}text-warning{% else %}text-success{% endif %}">
                            {{ alarm.person.name }}
                            {% if alarm.person.is_unknown %}
                                <small>(Unknown)</small>
                            {% endif %}
                        </span>
                    </td>
                    <td>
                        <span class="badge {% if alarm.alarm_type == 'ENTRY' %}bg-success{% elif alarm.alarm_type == 'EXIT' %}bg-danger{% endif %}">
                            {{ alarm.get_alarm_type_display }}
                        </span>
                        {% if alarm.is_auto_generated %}
                            <span class="text-danger ms-1" 
                                  title="{% if alarm.auto_generation_reason == 'MISSED_EXIT_AUTO' %}Otomatik exit (missed exit){% elif alarm.auto_generation_reason == 'MISSED_EXIT_REENTRY' %}Otomatik entry (exit kaçırıldı){% else %}Otomatik alarm{% endif %}"
                                  data-bs-toggle="tooltip">
                                <i class="bi bi-exclamation-triangle-fill"></i>
                            </span>
                        {% endif %}
                    </td>
                    <td>
                        {% if alarm.person.is_unknown %}
                            {% if alarm.alert_photo %}
                                <a href="{{ alarm.alert_photo.photo.url }}" target="_blank" class="btn btn-sm btn-outline-primary">View Photo</a>
                            {% elif alarm.video_snapshot %}
                                <a href="{{ alarm.video_snapshot.url }}" target="_blank" class="btn btn-sm btn-outline-primary">View Video</a>
                            {% else %}
                                <span class="text-muted">No snapshot</span>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7">No alerts found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </form>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1">&laquo; First</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
            </li>
            {% endif %}

            {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% else %}
                <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

</div>

<style>
    .alert-listing-container {
        padding: 20px;
    }
    button[type="submit"] {
        margin-right: 10px;
    }
    .badge {
        font-size: 0.75em;
    }
    .card-header h5 {
        color: #495057;
    }
    .btn-sm {
        font-size: 0.75rem;
    }
    .text-warning {
        font-weight: 500;
    }
    .text-success {
        font-weight: 500;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltip'leri aktifleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}