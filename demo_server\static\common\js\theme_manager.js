// Theme management for the application
class ThemeManager {
    constructor() {
        this.themeToggle = null;
        this.themeIcon = null;
        this.body = document.body;
        this.currentTheme = localStorage.getItem('theme') || 'light';
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeElements();
            this.setupEventListeners();
            this.setInitialTheme();
        });
    }

    initializeElements() {
        this.themeToggle = document.getElementById('theme-toggle');
        this.themeIcon = document.getElementById('theme-icon');
    }

    setupEventListeners() {
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    setTheme(theme) {
        this.body.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);
        
        if (theme === 'dark') {
            this.themeIcon.className = 'bi bi-moon-fill';
            this.body.classList.remove('bg-light');
            this.body.classList.add('bg-dark');
        } else {
            this.themeIcon.className = 'bi bi-sun-fill';
            this.body.classList.remove('bg-dark');
            this.body.classList.add('bg-light');
        }
    }

    setInitialTheme() {
        this.setTheme(this.currentTheme);
    }

    toggleTheme() {
        const newTheme = this.body.getAttribute('data-bs-theme') === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }
}

// Initialize theme manager
new ThemeManager();