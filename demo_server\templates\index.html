{% extends 'base.html' %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center py-5">
                <h1 class="display-4 fw-bold text-primary mb-3" data-aos="fade-down">
                    Welcome to Face Recognition System
                </h1>
                <p class="lead text-muted mb-4" data-aos="fade-up" data-aos-delay="100">
                    AI-powered face recognition and monitoring solution for enhanced security
                </p>
            </div>
        </div>
    </div>

    <!-- Feature Cards -->
    {% if user.is_authenticated %}
        <div class="row g-4 mb-5">
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-camera-video-fill text-white fs-4"></i>
                        </div>
                        <h5 class="card-title fw-bold">Camera Management</h5>
                        <a href="{% url 'cameras:index' %}" class="btn btn-outline-info">
                            <i class="bi bi-arrow-right me-1"></i>View Cameras
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-person-badge-fill text-white fs-4"></i>
                        </div>
                        <h5 class="card-title fw-bold">Person Management</h5>
                        <a href="{% url 'alerts:person_list' %}" class="btn btn-outline-success">
                            <i class="bi bi-arrow-right me-1"></i>Manage Persons
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card h-100 border-0 shadow-sm hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-warning bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-bell-fill text-white fs-4"></i>
                        </div>
                        <h5 class="card-title fw-bold">Alert Management</h5>
                        <a href="{% url 'alerts:alert_list' %}" class="btn btn-outline-warning">
                            <i class="bi bi-arrow-right me-1"></i>View Alerts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- User Profile Section -->
    {% if user.is_authenticated %}
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-header bg-gradient text-white border-0">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-circle me-2"></i>Profile Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-person-fill text-primary me-3 fs-5"></i>
                                    <div>
                                        <small class="text-muted">Username</small>
                                        <div class="fw-semibold">{{ user.username }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-envelope-fill text-primary me-3 fs-5"></i>
                                    <div>
                                        <small class="text-muted">Email</small>
                                        <div class="fw-semibold">{{ user.email|default:"Not provided" }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <!-- Access Required Section -->
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-body text-center p-5">
                        <i class="bi bi-shield-lock-fill text-primary mb-3" style="font-size: 3rem;"></i>
                        <h4 class="card-title fw-bold mb-3">Access Required</h4>
                        <p class="card-text text-muted mb-4">
                            Please log in through Keycloak
                        </p>
                        <div class="d-flex justify-content-center">
                            <a href="{% url 'oidc_authentication_init' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Login with Keycloak
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
