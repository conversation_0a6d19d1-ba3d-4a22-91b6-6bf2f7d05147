// Person list management functionality
class PersonListManager {
    constructor() {
        this.selectAllCheckbox = null;
        this.personCheckboxes = null;
        this.deleteSelectedBtn = null;
        this.personForm = null;
        
        this.init();
    }

    init() {
        // Use a slight delay to ensure all other scripts have loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.initialize(), 100);
            });
        } else {
            setTimeout(() => this.initialize(), 100);
        }
    }

    initialize() {
        this.initializeElements();
        this.setupEventListeners();
        console.log('PersonListManager initialized successfully');
    }

    initializeElements() {
        this.selectAllCheckbox = document.getElementById('select-all-checkbox');
        this.personCheckboxes = document.querySelectorAll('.person-checkbox');
        this.deleteSelectedBtn = document.getElementById('delete-selected-btn');
        this.personForm = document.getElementById('person-form');
        
        console.log('Elements found:', {
            selectAllCheckbox: !!this.selectAllCheckbox,
            personCheckboxes: this.personCheckboxes.length,
            deleteSelectedBtn: !!this.deleteSelectedBtn,
            personForm: !!this.personForm
        });
    }

    setupEventListeners() {
        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.addEventListener('change', () => {
                this.handleSelectAllChange();
            });
        }

        if (this.personCheckboxes) {
            this.personCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateDeleteButtonState();
                });
            });
        }

        if (this.deleteSelectedBtn) {
            this.deleteSelectedBtn.addEventListener('click', (e) => {
                this.handleDeleteSelectedClick(e);
            });
        }
        
        // Initial state update
        this.updateDeleteButtonState();
    }

    handleSelectAllChange() {
        const isChecked = this.selectAllCheckbox.checked;
        this.personCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        this.updateDeleteButtonState();
    }

    handleDeleteSelectedClick(e) {
        e.preventDefault();
        
        console.log('Delete button clicked');
        
        const checkedBoxes = document.querySelectorAll('.person-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('Please select at least one person to delete.');
            return;
        }
        
        // Use native confirm dialog for reliability
        const confirmMessage = `Are you sure you want to delete ${checkedBoxes.length} selected person(s)?\n\nThis will delete all related photos, alerts and face recognition data.`;
        
        if (confirm(confirmMessage)) {
            console.log('User confirmed deletion, submitting form');
            if (this.personForm) {
                this.personForm.submit();
            } else {
                console.error('Form not found');
                alert('Error: Form not found. Please refresh the page and try again.');
            }
        } else {
            console.log('User cancelled deletion');
        }
    }

    updateDeleteButtonState() {
        const checkedBoxes = document.querySelectorAll('.person-checkbox:checked');
        
        if (this.deleteSelectedBtn) {
            this.deleteSelectedBtn.disabled = checkedBoxes.length === 0;
        }

        if (this.selectAllCheckbox && this.personCheckboxes) {
            this.selectAllCheckbox.checked = this.personCheckboxes.length > 0 &&
                                           checkedBoxes.length === this.personCheckboxes.length;
            this.selectAllCheckbox.indeterminate = checkedBoxes.length > 0 &&
                                                  checkedBoxes.length < this.personCheckboxes.length;
        }
    }
}

// Initialize PersonListManager only if we're on the person list page
if (document.getElementById('person-form')) {
    new PersonListManager();
}