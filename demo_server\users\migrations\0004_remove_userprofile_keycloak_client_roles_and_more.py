# Generated by Django 5.1.4 on 2025-09-29 08:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_userprofile_keycloak_client_roles_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userprofile',
            name='keycloak_client_roles',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='keycloak_realm_roles',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='permissions',
        ),
        migrations.AddField(
            model_name='userprofile',
            name='assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_roles', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='role_assigned_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='UserRoleHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role_name', models.CharField(max_length=100)),
                ('action', models.CharField(choices=[('assigned', 'Assigned'), ('removed', 'Removed')], max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(blank=True)),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='role_assignments_made', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Role History',
                'verbose_name_plural': 'User Role Histories',
                'ordering': ['-timestamp'],
            },
        ),
    ]
