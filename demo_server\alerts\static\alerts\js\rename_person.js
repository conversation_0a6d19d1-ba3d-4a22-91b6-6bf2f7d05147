document.addEventListener('DOMContentLoaded', function() {
    console.log('Rename person JavaScript loaded');
    
    const renameTypeRadios = document.querySelectorAll('input[name="rename_type"]');
    const newNameGroup = document.getElementById('new-name-group');
    const existingPersonGroup = document.getElementById('existing-person-group');
    const newNameField = document.getElementById('id_new_name');
    const existingPersonField = document.getElementById('id_existing_person');
    
    // Debug: Check if elements exist
    console.log('Found elements:', {
        radios: renameTypeRadios.length,
        newNameGroup: !!newNameGroup,
        existingPersonGroup: !!existingPersonGroup,
        newNameField: !!newNameField,
        existingPersonField: !!existingPersonField
    });
    
    function toggleFields() {
        const selectedRadio = document.querySelector('input[name="rename_type"]:checked');
        
        if (!selectedRadio) {
            console.warn('No radio button selected');
            return;
        }
        
        const selectedValue = selectedRadio.value;
        console.log('Selected value:', selectedValue);
        
        if (selectedValue === 'new') {
            // Show new name field, hide existing person field
            if (newNameGroup) newNameGroup.style.display = 'block';
            if (existingPersonGroup) existingPersonGroup.style.display = 'none';
            
            // Update required attributes
            if (newNameField) newNameField.required = true;
            if (existingPersonField) existingPersonField.required = false;
            
            console.log('Switched to new person mode');
        } else if (selectedValue === 'existing') {
            // Hide new name field, show existing person field
            if (newNameGroup) newNameGroup.style.display = 'none';
            if (existingPersonGroup) existingPersonGroup.style.display = 'block';
            
            // Update required attributes
            if (newNameField) newNameField.required = false;
            if (existingPersonField) existingPersonField.required = true;
            
            console.log('Switched to existing person mode');
        }
    }
    
    // Add event listeners to radio buttons
    if (renameTypeRadios.length > 0) {
        renameTypeRadios.forEach((radio, index) => {
            console.log(`Adding event listener to radio ${index}:`, radio.value);
            radio.addEventListener('change', function() {
                console.log('Radio changed:', this.value);
                toggleFields();
            });
        });
    } else {
        console.error('No radio buttons found with name="rename_type"');
    }
    
    // Initialize the form state
    toggleFields();
    
    // Additional debugging: Log current form state
    setTimeout(() => {
        const currentSelection = document.querySelector('input[name="rename_type"]:checked');
        console.log('Initial form state:', {
            selected: currentSelection ? currentSelection.value : 'none',
            newNameVisible: newNameGroup ? newNameGroup.style.display : 'unknown',
            existingPersonVisible: existingPersonGroup ? existingPersonGroup.style.display : 'unknown'
        });
    }, 100);
});