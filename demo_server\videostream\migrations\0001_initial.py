# Generated by Django 5.1.4 on 2025-05-29 09:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cameras', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Zone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points', models.JSONField()),
                ('color', models.CharField(max_length=7)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zones', to='cameras.camera')),
            ],
        ),
    ]
