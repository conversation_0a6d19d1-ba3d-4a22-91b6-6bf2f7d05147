# Generated by Django 5.1.4 on 2025-06-26 20:25

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0003_delete_persontimelog'),
        ('cameras', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PersonTimeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('entry', 'Entry'), ('exit', 'Exit')], max_length=10)),
                ('timestamp', models.DateTimeField()),
                ('confidence', models.FloatField(default=0.0, help_text='Recognition confidence score', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cameras.camera')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_logs', to='alerts.alertperson')),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['person', 'timestamp'], name='alerts_pers_person__c2ef76_idx'), models.Index(fields=['camera', 'timestamp'], name='alerts_pers_camera__0dce25_idx'), models.Index(fields=['person', 'camera', 'timestamp'], name='alerts_pers_person__f64f2f_idx')],
            },
        ),
    ]
