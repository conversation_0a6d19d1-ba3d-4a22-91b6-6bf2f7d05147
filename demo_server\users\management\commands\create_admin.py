"""
Django management command to create a superuser (admin) account.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserProfile, UserRoleHistory


class Command(BaseCommand):
    help = "Create a new admin (superuser) account via CLI."

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, required=True, help='Username for the admin')
        parser.add_argument('--password', type=str, required=True, help='Password for the admin')
        parser.add_argument('--email', type=str, default='', help='Optional email for the admin')

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']
        email = options['email']

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.ERROR(f"User '{username}' already exists.")
            )
            return

        # Create superuser
        user = User.objects.create_superuser(
            username=username,
            password=password,
            email=email
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"Superuser '{username}' created successfully."
            )
        )

        # Ensure UserProfile exists
        UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'preferred_recognizer': 'facenet',
                'is_organizational_admin': True  # admin olduğu için True yapabiliriz
            }
        )

        # Add entry to UserRoleHistory (audit trail)
        UserRoleHistory.objects.create(
            user=user,
            role_name='superuser',
            action='created',
            assigned_by=None,  # CLI üzerinden yaratıldığı için None
            reason='Created via management command create_admin'
        )

        self.stdout.write(
            self.style.WARNING(
                f"Audit trail: Role history entry created for '{username}' (superuser)."
            )
        )
