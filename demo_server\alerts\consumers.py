import json
from channels.generic.websocket import AsyncWebsocketConsumer

class AlertConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        print("connect")
        self.user = self.scope["user"]
        if self.user.is_authenticated:
            # Kullanıcı kimlik doğrulaması başarılı ise kanal grubuna katıl
            await self.channel_layer.group_add(
                f"user_{self.user.id}",  # Kanal grubuna katıl
                self.channel_name
            )
            await self.accept()  # WebSocket bağlantısını kabul et
        else:
            await self.close()  # <PERSON>lik doğrulaması başarısızsa bağlantıyı kapat

    async def disconnect(self, close_code):
        # Kullanıc<PERSON> çıkarsa, kanal grubundan çıkar
        if self.user.is_authenticated:
            await self.channel_layer.group_discard(
                f"user_{self.user.id}",
                self.channel_name
            )

    async def receive(self, text_data):
        # WebSocket üzerinden gelen mesajı al
        text_data_json = json.loads(text_data)
        alert_message = text_data_json.get('message')

        # Gelen mesajı kanal grubuna gönder (örneğin tüm bağlı kullanıcılara)
        if alert_message:
            await self.channel_layer.group_send(
                f"user_{self.user.id}",  # Belirli bir kullanıcıya mesaj gönder
                {
                    'type': 'send_alert',
                    'message': alert_message
                }
            )

    async def send_alert(self, event):
        # Kanal grubundan gelen mesajı WebSocket'e ilet
        await self.send(text_data=json.dumps({
            'type': 'alert',  # Mesaj türü
            'message': event['message']  # Gelen mesaj
        }))