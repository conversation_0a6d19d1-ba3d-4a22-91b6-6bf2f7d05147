import cv2
import numpy as np
from PIL import Image
from django.conf import settings
from django.core.files.base import ContentFile
from .models import Alarm
from recognition.facenet_recognizer import FaceNet<PERSON><PERSON>ognizer
from cameras.models import Camera
from django.utils import timezone

# Initialize the recognizer
facenet_recognizer = FaceNetRecognizer()

def calculate_image_vector(face_image):
    """Given a face image, calculate its vector using the recognizer."""
    # The image is already a numpy array (face_image)
    vector = facenet_recognizer.represent(img_path=face_image)
    return vector

def detect_faces(frame):
    """Detect faces in a given frame."""
    # The frame is a numpy array
    bboxes, landmarks = facenet_recognizer.detect_faces(frame)
    return bboxes, landmarks

def recognize_face(image):
    """Recognize faces in a given image."""
    bboxes, names, scores = facenet_recognizer.recognize(image)
    return bboxes, names, scores

def add_face(name, filepath):
    """Add a face to the recognizer."""
    facenet_recognizer.add_face(name, filepath)