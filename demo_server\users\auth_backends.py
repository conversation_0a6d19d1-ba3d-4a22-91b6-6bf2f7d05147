"""
Custom Keycloak OIDC Authentication Backend for Django Face Recognition System
"""

from mozilla_django_oidc.auth import OIDCAuthenticationBackend
from django.contrib.auth.models import User
from django.utils import timezone
import logging

from .models import UserProfile

logger = logging.getLogger(__name__)


class KeycloakOIDCAuthenticationBackend(OIDCAuthenticationBackend):
    """
    Custom OIDC Authentication Backend for Keycloak integration.
    - Authentication: always via Keycloak (OIDC).
    - Authorization (permissions/roles): handled in Django via Groups.
    - Keycloak 'sub' claim is the unique and permanent user identifier.
    """

    def get_username(self, claims):
        """
        Use Keycloak 'preferred_username' as the Django username for better display.
        Fall back to email or sub if preferred_username is not available.
        """
        return claims.get('preferred_username') or claims.get('email') or claims.get('sub')

    def create_user(self, claims):
        """
        Create a new user in Django when authenticating for the first time.
        """
        username = self.get_username(claims)
        email = claims.get('email', '')
        display_name = claims.get('preferred_username') or claims.get('email', username)

        # Create the Django user
        user = User.objects.create_user(
            username=username,
            email=email,
            first_name=display_name,  # optional: use preferred_username as first_name
            password=None  # unusable password, since login is only via Keycloak
        )

        # Create a related UserProfile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'preferred_recognizer': 'facenet',
                'is_organizational_admin': False,
                'keycloak_id': claims.get('sub')  # Still store sub for unique identification
            }
        )

        # Sync profile fields from Keycloak
        if hasattr(profile, "update_from_keycloak"):
            profile.update_from_keycloak(claims)

        logger.info(f"[Keycloak] Created new user {username} with sub={claims.get('sub')}")
        return user

    def update_user(self, user, claims):
        """
        Update an existing user when they log in again via Keycloak.
        """
        # Update Django user fields
        user.email = claims.get('email', user.email)
        user.first_name = claims.get('preferred_username') or user.first_name
        user.last_login = timezone.now()
        user.save(update_fields=['email', 'first_name', 'last_login'])

        # Ensure UserProfile exists
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'preferred_recognizer': 'facenet',
                'is_organizational_admin': False,
                'keycloak_id': claims.get('sub')
            }
        )

        if hasattr(profile, "update_from_keycloak"):
            profile.update_from_keycloak(claims)

        logger.info(f"[Keycloak] Updated user {user.username} (sub={claims.get('sub')})")
        return user

    def filter_users_by_claims(self, claims):
        """
        Find the Django user corresponding to the Keycloak 'sub'.
        This ensures one-to-one mapping between Keycloak and Django.
        """
        sub = claims.get('sub')
        if not sub:
            return self.UserModel.objects.none()

        try:
            profile = UserProfile.objects.get(keycloak_id=sub)
            return self.UserModel.objects.filter(pk=profile.user.pk)
        except UserProfile.DoesNotExist:
            try:
                # Fallback: check if Django user has username=preferred_username
                username = self.get_username(claims)
                user = self.UserModel.objects.get(username=username)
                return self.UserModel.objects.filter(pk=user.pk)
            except self.UserModel.DoesNotExist:
                return self.UserModel.objects.none()

    def authenticate(self, request, **credentials):
        """
        Authenticate user via Keycloak OIDC.
        """
        user = super().authenticate(request, **credentials)
        if user:
            logger.info(f"[Keycloak] User {user.username} authenticated successfully")
        else:
            logger.warning("[Keycloak] Authentication failed")
        return user

    def describe_user_by_claims(self, claims):
        """
        Return a description of the user based on claims (for logging/debug).
        """
        return f"Keycloak user with sub={claims.get('sub')} ({claims.get('preferred_username') or claims.get('email')})"
