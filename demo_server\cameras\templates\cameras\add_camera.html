{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2><i class="bi bi-camera-video"></i> <PERSON><PERSON></h2>
            <p class="text-muted">Sisteme yeni bir kamera ekleyin ve bir mekana atayın</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Kamera Bilgileri</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                      
                        <!-- Ka<PERSON>a Adı -->
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>

                        <!-- RTSP URL -->
                        <div class="mb-3">
                            <label for="id_rtsp_url" class="form-label">RTSP URL</label>
                            <input type="text" name="{{ form.rtsp_url.name }}" id="id_rtsp_url" class="form-control" {% if form.rtsp_url.value %}value="{{ form.rtsp_url.value }}"{% endif %}>
                        </div>

                        <!-- Mekan -->
                        <div class="mb-3">
                            <label for="{{ form.venue.id_for_label }}" class="form-label">
                                {{ form.venue.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.venue }}
                            {% if form.venue.help_text %}
                                <div class="form-text">{{ form.venue.help_text }}</div>
                            {% endif %}
                            {% if form.venue.errors %}
                                <div class="text-danger">{{ form.venue.errors }}</div>
                            {% endif %}
                            {% if not form.venue.queryset %}
                                <div class="alert alert-warning mt-2">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    Henüz hiç mekan oluşturmamışsınız. 
                                    <a href="{% url 'alerts:venue_create' %}" class="alert-link">Önce bir mekan oluşturun</a>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Kamera Tipi -->
                        <div class="mb-3">
                            <label for="{{ form.camera_type.id_for_label }}" class="form-label">
                                {{ form.camera_type.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.camera_type }}
                            {% if form.camera_type.help_text %}
                                <div class="form-text">{{ form.camera_type.help_text }}</div>
                            {% endif %}
                            {% if form.camera_type.errors %}
                                <div class="text-danger">{{ form.camera_type.errors }}</div>
                            {% endif %}
                        </div>

                        <!-- Butonlar -->
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> Kamerayı Ekle
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="{% url 'cameras:index' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

