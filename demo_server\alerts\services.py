"""
Employee Photo Service for fetching employee photos from company API
"""
import requests
import logging
import base64
from io import BytesIO
from PIL import Image
from django.conf import settings
from django.core.cache import cache
from typing import Optional, List, Dict

logger = logging.getLogger(__name__)


class EmployeePhotoService:
    """
    Service class for fetching employee photos from company API
    
    Features:
    - Fetches photos from company REST API
    - Caches photos to reduce API calls
    - Handles API errors gracefully
    - Returns base64 encoded images for template usage
    """
    
    def __init__(self):
        self.api_config = getattr(settings, 'EMPLOYEE_PHOTO_API', {})
        self.endpoint = self.api_config.get('ENDPOINT')
        self.timeout = self.api_config.get('TIMEOUT', 10)
        self.cache_duration = self.api_config.get('CACHE_DURATION', 3600)
        self.enabled = self.api_config.get('ENABLED', True)
        
        # Endpoint gerekli, yoksa disable et
        if not self.endpoint:
            self.enabled = False
            logger.warning("Employee photo API disabled: No endpoint configured")
        
    def is_enabled(self) -> bool:
        """Check if employee photo API is enabled"""
        return self.enabled
    
    def _fetch_employee_data(self) -> Optional[List[Dict]]:
        """
        Fetch all employee data from company API
        
        Returns:
            List of employee data or None if failed
        """
        if not self.enabled:
            logger.info("Employee photo API is disabled")
            return None
            
        cache_key = 'employee_photos_data'
        cached_data = cache.get(cache_key)
        
        if cached_data is not None:
            logger.debug("Using cached employee data")
            return cached_data
            
        try:
            logger.info(f"Fetching employee data from API: {self.endpoint}")
            response = requests.get(
                self.endpoint,
                timeout=self.timeout,
                headers={
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            )
            response.raise_for_status()
            
            data = response.json()
            
            if not isinstance(data, list):
                logger.error(f"API returned unexpected data format: {type(data)}")
                return None
                
            # Cache the successful response
            cache.set(cache_key, data, self.cache_duration)
            logger.info(f"Successfully fetched {len(data)} employee records")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Failed to fetch employee data: {str(e)}")
            return None
        except ValueError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching employee data: {str(e)}")
            return None
    
    def get_employee_photo_by_id(self, employee_id: str) -> Optional[str]:
        """
        Get employee photo by employee ID
        
        Args:
            employee_id: The employee ID to search for
            
        Returns:
            Base64 encoded photo data or None if not found
        """
        if not self.enabled:
            return None
            
        if not employee_id or not isinstance(employee_id, str):
            logger.warning(f"Invalid employee_id provided: {employee_id}")
            return None
            
        cache_key = f'employee_photo_{employee_id}'
        cached_photo = cache.get(cache_key)
        
        if cached_photo is not None:
            logger.debug(f"Using cached photo for employee: {employee_id}")
            return cached_photo
            
        employee_data = self._fetch_employee_data()
        if not employee_data:
            return None
            
        # Find employee by ID
        employee = None
        for emp in employee_data:
            if str(emp.get('employeeId', '')) == str(employee_id):
                employee = emp
                break
                
        if not employee:
            logger.info(f"Employee not found in API data: {employee_id}")
            # Cache negative result to avoid repeated API calls
            cache.set(cache_key, None, self.cache_duration)
            return None
            
        photo_data = employee.get('photodata')
        if not photo_data:
            logger.info(f"No photo data found for employee: {employee_id}")
            cache.set(cache_key, None, self.cache_duration)
            return None
            
        try:
            # Validate base64 data
            base64.b64decode(photo_data)
            
            # Cache the photo
            cache.set(cache_key, photo_data, self.cache_duration)
            logger.info(f"Successfully retrieved photo for employee: {employee_id}")
            
            return photo_data
            
        except Exception as e:
            logger.error(f"Invalid base64 photo data for employee {employee_id}: {str(e)}")
            cache.set(cache_key, None, self.cache_duration)
            return None
    
    def get_employee_photo_by_name(self, name: str) -> Optional[str]:
        """
        Get employee photo by name (if name matches employeeId)
        This is a fallback method for name-based lookups
        
        Args:
            name: The name/ID to search for
            
        Returns:
            Base64 encoded photo data or None if not found
        """
        # Try to use name as employee ID
        return self.get_employee_photo_by_id(name)
    
    def clear_cache(self) -> None:
        """Clear all cached employee photo data"""
        cache.delete('employee_photos_data')
        logger.info("Cleared employee photo cache")
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics for debugging"""
        employee_data = cache.get('employee_photos_data')
        return {
            'api_enabled': self.enabled,
            'endpoint': self.endpoint,
            'cache_duration': self.cache_duration,
            'has_cached_data': employee_data is not None,
            'cached_employee_count': len(employee_data) if employee_data else 0,
        }


# Global service instance
employee_photo_service = EmployeePhotoService()