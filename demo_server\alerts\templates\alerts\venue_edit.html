{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <h2><i class="bi bi-pencil"></i> <PERSON><PERSON></h2>
            <p class="text-muted">Mekan bilgilerini güncelleyin</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{% url 'alerts:venue_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Mekan Listesi
            </a>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Me<PERSON> Bilgiler<PERSON></h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                Mekan Adı <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   required 
                                   maxlength="100"
                                   value="{{ venue.name }}"
                                   placeholder="örn: Ana Ofis, 1. Kat, Toplantı Salonu">
                            <div class="form-text">Mekanınızı tanımlayıcı bir isim verin</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Açıklama</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Mekan hakkında detayları yazabilirsiniz (opsiyonel)">{{ venue.description }}</textarea>
                            <div class="form-text">Mekanın özelliklerini, konumunu veya diğer detayları açıklayabilirsiniz</div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       {% if venue.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    <i class="bi bi-check-circle text-success"></i> Mekan aktif
                                </label>
                                <div class="form-text">Pasif mekanlar için kamera ataması yapılamaz</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> Değişiklikleri Kaydet
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="{% url 'alerts:venue_list' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg"></i> İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Mekan İstatistikleri -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-1">
                            <i class="bi bi-info-circle text-info display-6"></i>
                        </div>
                        <div class="col-md-11">
                            <h6>Mekan İstatistikleri</h6>
                            <ul class="mb-0">
                                <li><strong>{{ camera_count }}</strong> kamera bu mekana atanmış</li>
                                {% if camera_count > 0 %}
                                    <li>Entry kameralar: <strong>{{ venue.get_entry_cameras.count }}</strong></li>
                                    <li>Exit kameralar: <strong>{{ venue.get_exit_cameras.count }}</strong></li>
                                {% endif %}
                                <li>Oluşturulma: <strong>{{ venue.created_at|date:"d.m.Y H:i" }}</strong></li>
                                <li>Son güncelleme: <strong>{{ venue.updated_at|date:"d.m.Y H:i" }}</strong></li>
                            </ul>
                            
                            {% if camera_count > 0 and not venue.is_active %}
                                <div class="alert alert-warning mt-3 mb-0">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    Bu mekanı pasif yaparsanız, atanmış kameralar entry/exit tracking yapamayacak.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}