{% extends 'base.html' %}

{% block content %}
<!-- <PERSON> ba<PERSON><PERSON><PERSON>k kartı -->
<div class="container-fluid d-flex justify-content-center align-items-center mb-4">
    <div class="name-card">
        <h1 class="person-title">{{ person.name }}</h1>
    </div>
</div>

<div class="alert-container">
    <h1 class="alert-title">
        <i class="bi bi-cloud-upload me-2"></i>Add Photos
    </h1>
    
    <!-- Basic Django Form Upload -->
    <div class="photo-upload-form">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_photos" class="form-label">Select Photos</label>
                {{ form.photos }}
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-upload me-2"></i>Upload Photos
            </button>
        </form>
    </div>

    <h1 class="alert-title">Existing Photos</h1>

    <!-- Photo Grid -->
    <div class="photo-gallery">
        <div class="row">
            {% for photo in person.photos.all %}
            <div class="col-md-3 col-sm-6 mb-4">
                <div class="photo-card card h-100">
                    <div class="photo-container">
                        <img src="{{ photo.photo.url }}" alt="{{ person.name }}" 
                             class="card-img-top photo-thumbnail">
                        {% if photo.is_primary %}
                        <span class="badge bg-primary position-absolute top-0 end-0 m-2">Primary</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <p class="card-text">Added: {{ photo.created_at|date:"d M Y, H:i" }}</p>
                        <div class="d-flex justify-content-end mt-2">
                            <a href="{% url 'alerts:delete_photo' photo_id=photo.id %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> Sil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <p class="paragraph">No photos uploaded yet.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}