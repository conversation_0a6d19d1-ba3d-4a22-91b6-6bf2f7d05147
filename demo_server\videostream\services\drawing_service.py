import logging
import cv2
import numpy as np

logger = logging.getLogger(__name__)


class DrawingService:
    """Service for handling frame annotation and drawing operations"""
    
    @staticmethod
    def annotate_frame(frame, bboxes, names, confidences):
        """
        Draw face bounding boxes and labels on frame
        
        Args:
            frame: Input video frame
            bboxes: Array of face bounding boxes
            names: List of person names
            confidences: List of confidence scores
            
        Returns:
            Annotated frame
        """
        try:
            if bboxes is None or len(bboxes) == 0:
                return frame
            
            frame_with_boxes = frame.copy()
            
            for bbox, name, confidence in zip(bboxes, names, confidences):
                x1, y1, x2, y2 = map(int, bbox[:4])
                
                # Draw face rectangle
                cv2.rectangle(frame_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Convert confidence to percentage for display
                confidence_percentage = confidence * 100
                
                # Draw name and confidence text
                label = f"{name} ({confidence_percentage:.0f}%)"
                cv2.putText(
                    frame_with_boxes,
                    label,
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 255, 0),
                    2
                )
            
            logger.debug(f"Annotated frame with {len(names)} faces")
            return frame_with_boxes
            
        except Exception as e:
            logger.error(f"Error annotating frame: {str(e)}")
            return frame
    
    @staticmethod
    def draw_zones(frame, zones):
        """
        Draw zone boundaries on frame (for debugging)
        
        Args:
            frame: Input video frame
            zones: List of zone definitions
            
        Returns:
            Frame with zone boundaries drawn
        """
        try:
            if not zones:
                return frame
            
            frame_with_zones = frame.copy()
            
            for i, zone in enumerate(zones):
                points = np.array(zone['points'], np.int32)
                points = points.reshape((-1, 1, 2))
                
                # Draw zone polygon
                cv2.polylines(frame_with_zones, [points], True, (255, 0, 0), 2)
                
                # Draw zone label
                if len(points) > 0:
                    x, y = points[0][0]
                    cv2.putText(
                        frame_with_zones,
                        f"Zone {i+1}",
                        (x, y - 10),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        (255, 0, 0),
                        2
                    )
            
            return frame_with_zones
            
        except Exception as e:
            logger.error(f"Error drawing zones: {str(e)}")
            return frame
