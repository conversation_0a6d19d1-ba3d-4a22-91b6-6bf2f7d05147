from django.db import models
from django.contrib.auth.models import User, Group
from django.core.exceptions import ValidationError
from django.utils import timezone
import json

# Create your models here.

class UserProfile(models.Model):
    RECOGNIZER_CHOICES = [
        ('facenet', 'FaceNetRecognizer'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    preferred_recognizer = models.CharField(
        max_length=20,
        choices=RECOGNIZER_CHOICES,
        default='facenet',
    )
    is_organizational_admin = models.BooleanField(default=False)
    
    # Keycloak integration fields
    keycloak_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    last_keycloak_sync = models.DateTimeField(null=True, blank=True)
    
    # Role assignment tracking
    assigned_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_roles'
    )
    role_assigned_date = models.DateTime<PERSON>ield(null=True, blank=True)
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    
    def __str__(self):
        return self.user.username
    
    @property
    def current_roles(self):
        """Get current Django groups (roles) for the user"""
        return self.user.groups.all()
    
    @property
    def role_names(self):
        """Get list of role names"""
        return list(self.user.groups.values_list('name', flat=True))
    
    def has_role(self, role_name):
        """Check if user has a specific role (group)"""
        return self.user.groups.filter(name=role_name).exists()
    
    def assign_role(self, role_name, assigned_by_user=None):
        """Assign a role (Group) to the user"""
        try:
            group = Group.objects.get(name=role_name)
            if not self.user.groups.filter(name=role_name).exists():
                self.user.groups.add(group)
                self.assigned_by = assigned_by_user
                self.role_assigned_date = timezone.now()
                self.save()
                
                # History kaydı
                UserRoleHistory.objects.create(
                    user=self.user,
                    role_name=role_name,
                    action='assigned',
                    assigned_by=assigned_by_user,
                    reason=f'Role {role_name} assigned'
                )
                return True
        except Group.DoesNotExist:
            return False
        return False
    
    def remove_role(self, role_name, removed_by_user=None, reason=""):
        """Remove a role from the user"""
        try:
            group = Group.objects.get(name=role_name)
            if self.user.groups.filter(name=role_name).exists():
                self.user.groups.remove(group)
                UserRoleHistory.objects.create(
                    user=self.user,
                    role_name=role_name,
                    action='removed',
                    assigned_by=removed_by_user,
                    reason=reason or f'Role {role_name} removed'
                )
                return True
        except Group.DoesNotExist:
            return False
        return False
    
    # ------------------------
    # Permission Checks (Django default permissions)
    # ------------------------
    def has_permission(self, full_codename: str) -> bool:
        """
        Check if user has a specific permission.
        Example: 'cameras.view_camera', 'alerts.change_alertperson'
        """
        return self.user.has_perm(full_codename) or self.user.is_superuser
    
    def can_access_cameras(self):
        """Check if user can view or manage cameras"""
        return (
            self.user.has_perm('cameras.view_camera') or
            self.user.has_perm('cameras.change_camera') or
            self.user.is_superuser
        )
    
    def can_access_persons(self):
        """Check if user can view or manage persons (alerts)"""
        return (
            self.user.has_perm('alerts.view_alertperson') or
            self.user.has_perm('alerts.change_alertperson') or
            self.user.is_superuser
        )
    
    def can_manage_users(self):
        """Check if user can view or change Django Users"""
        return (
            self.user.has_perm('auth.view_user') or
            self.user.has_perm('auth.change_user') or
            self.user.is_superuser
        )
    
    def update_from_keycloak(self, user_info):
        """Update user profile based on Keycloak claims"""
        if 'email' in user_info:
            self.user.email = user_info['email']
        if 'given_name' in user_info:
            self.user.first_name = user_info['given_name']
        if 'family_name' in user_info:
            self.user.last_name = user_info['family_name']
        
        if 'sub' in user_info:
            self.keycloak_id = user_info['sub']
        
        self.last_keycloak_sync = timezone.now()
        self.user.save()
        self.save()

class UserRoleHistory(models.Model):
    """Track role assignment and removal history"""
    ACTION_CHOICES = [
        ('assigned', 'Assigned'),
        ('removed', 'Removed'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='role_history')
    role_name = models.CharField(max_length=100)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    assigned_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='role_assignments_made'
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'User Role History'
        verbose_name_plural = 'User Role Histories'
    
    def __str__(self):
        return f"{self.user.username} - {self.role_name} - {self.action}"