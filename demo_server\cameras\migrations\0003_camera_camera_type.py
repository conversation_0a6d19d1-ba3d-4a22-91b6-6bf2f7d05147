# Generated by Django 5.1.4 on 2025-09-16 08:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0002_remove_guest_flat_remove_camera_camera_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='camera',
            name='camera_type',
            field=models.CharField(choices=[('ENTRY', 'Entry Camera'), ('EXIT', 'Exit Camera'), ('BOTH', 'Entry & Exit Camera')], default='ENTRY', help_text='Camera type for visit tracking', max_length=10),
        ),
    ]
