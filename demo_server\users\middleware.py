"""
Custom middleware for Django Groups-based access control
"""

import logging
import re
from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.urls import resolve, reverse
from django.contrib import messages
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class RoleBasedAccessMiddleware(MiddlewareMixin):
    """
    Middleware to enforce role-based access control using Django's built-in permissions.
    """

    # URL patterns mapped to Django built-in permissions
    PERMISSION_MAP = {
    # Cameras
    r"^/camera/$": "cameras.view_camera",
    r"^/camera/add/": "cameras.add_camera",
    r"^/camera/update/": "cameras.change_camera",
    r"^/camera/delete/": "cameras.delete_camera",
    r"^/camera/check_status/": "cameras.view_camera",

    # Alerts (Persons & Photos & Alarms)
    r"^/alert/$": "alerts.view_alertperson",  # index sayfası listeleme
    r"^/alert/persons/": "alerts.view_alertperson",
    r"^/alert/person/add/": "alerts.add_alertperson",
    r"^/alert/person/[0-9]+/": "alerts.view_alertperson",  # detail
    r"^/alert/person/[0-9]+/times/": "alerts.view_alarm",
    r"^/alert/persons/delete-selected/": "alerts.delete_alertperson",
    r"^/alert/photos/delete/[0-9]+/": "alerts.delete_alertphoto",
    r"^/alert/alerts/": "alerts.view_alarm",
    r"^/alert/delete-multiple-alerts/": "alerts.delete_alarm",
    r"^/alert/unknown_persons/": "alerts.view_alertperson",
    r"^/alert/unknown_persons/[0-9]+/": "alerts.view_alertperson",
    r"^/alert/unknown_persons/[0-9]+/rename/": "alerts.change_alertperson",

    # Videostream (zones & viewer)
    r"^/videostream/viewer/": "videostream.view_zone",      # sadece stream izleme
    r"^/videostream/start/": "videostream.change_zone",     # stream açma
    r"^/videostream/stop/": "videostream.change_zone",
    r"^/videostream/stream/": "videostream.view_zone",
    r"^/videostream/zone_creator/": "videostream.add_zone",
    r"^/videostream/save_zones/": "videostream.add_zone",
    r"^/videostream/get_zones/": "videostream.view_zone",
    r"^/videostream/toggle_zone_filter/": "videostream.change_zone",
    r"^/videostream/update_fps/": "videostream.change_zone",

    # Users (management)
    r"^/users/manage/": "auth.change_user",
    r"^/users/assign-role/": "auth.change_user",
    r"^/users/remove-role/": "auth.change_user",
    r"^/users/[0-9]+/permissions/": "auth.view_user",

    # Admin (only superuser)
    r"^/admin/": "admin",
    }


    # URLs that don't require authentication
    PUBLIC_URLS = [
        "/users/login/",
        "/oidc/",
        "/users/logout/",
        "/static/",
        "/media/",
    ]

    def process_request(self, request):
        """
        Process the request and check permissions.
        """
        path = request.path_info

        # Skip for public URLs
        if any(path.startswith(url) for url in self.PUBLIC_URLS):
            return None

        # If user not authenticated → no permission checks (handled by login_required)
        if not request.user.is_authenticated:
            return None

        # Superuser bypass
        if request.user.is_superuser:
            return None

        # Check required permission
        required_permission = self.get_required_permission(path)
        if required_permission:
            if not self.user_has_permission(request.user, required_permission):
                logger.warning(
                    f"User {request.user.username} denied access to {path} - missing permission: {required_permission}"
                )

                # API isteği mi kontrol et (ör. AJAX/REST çağrıları)
                if request.headers.get("x-requested-with") == "XMLHttpRequest" or path.startswith("/api/"):
                    return HttpResponseForbidden("Permission denied.")

                # Normal web isteği → redirect with message
                messages.error(
                    request,
                    f"You don't have permission to access this resource. Required permission: {required_permission}"
                )
                return redirect("index")

        return None

    def get_required_permission(self, path):
        """
        Get the required permission for a given URL path.
        """
        for pattern, permission in self.PERMISSION_MAP.items():
            if re.match(pattern, path):
                return permission
        return None

    def user_has_permission(self, user, permission):
        """
        Check if user has the required permission.
        """
        try:
            if permission == "admin":
                return user.is_superuser
            return user.has_perm(permission)
        except Exception as e:
            logger.error(f"Error checking permissions for user {user.username}: {str(e)}")
            return False


def require_permission(permission_codename):
    """
    Decorator to require specific permission for a view.
    Reuses the same logic from middleware (DRY).
    """
    def decorator(view_func):
        def wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect("users:login")

            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Use the same logic as middleware
            if not request.user.has_perm(permission_codename):
                messages.error(
                    request,
                    f"You don't have permission to access this resource. Required permission: {permission_codename}"
                )
                return redirect("index")

            return view_func(request, *args, **kwargs)
        return wrapped_view
    return decorator
