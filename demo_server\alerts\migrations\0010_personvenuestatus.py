# Generated by Django 5.1.4 on 2025-09-18 10:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0009_add_venue_model'),
        ('cameras', '0004_add_venue_field_nullable'),
    ]

    operations = [
        migrations.CreateModel(
            name='PersonVenueStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('OUTSIDE', 'Outside'), ('INSIDE', 'Inside')], default='OUTSIDE', help_text='Current status of person in this venue', max_length=10)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_camera', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='cameras.camera')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venue_statuses', to='alerts.alertperson')),
                ('venue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='person_statuses', to='alerts.venue')),
            ],
            options={
                'indexes': [models.Index(fields=['person', 'venue', 'status'], name='alerts_pers_person__efced0_idx'), models.Index(fields=['venue', 'status'], name='alerts_pers_venue_i_e8cfd5_idx')],
                'unique_together': {('person', 'venue')},
            },
        ),
    ]
