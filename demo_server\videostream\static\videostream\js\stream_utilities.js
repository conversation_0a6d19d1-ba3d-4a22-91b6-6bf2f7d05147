// Stream utilities for handling stream loading states and connection status
class StreamUtilities {
    constructor() {
        this.streamImg = null;
        this.connectionIndicator = null;
        this.connectionText = null;
        this.streamResolution = null;
        this.streamUrl = null;
        this.cameraId = null;
        
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeElements();
            this.setupEventListeners();
            this.autoStartStream();
            this.initializeZoneFilter();
        });
    }

    initializeElements() {
        this.streamImg = document.getElementById('stream');
        this.connectionIndicator = document.getElementById('connection-indicator');
        this.connectionText = document.getElementById('connection-text');
        this.streamResolution = document.getElementById('stream-resolution');
        
        const streamUrlElement = document.getElementById('stream-url');
        const cameraIdElement = document.getElementById('camera-id');
        
        this.streamUrl = streamUrlElement ? streamUrlElement.value : null;
        this.cameraId = cameraIdElement ? cameraIdElement.value : null;
    }

    setupEventListeners() {
        if (!this.streamImg) return;

        // Show loading state
        this.streamImg.addEventListener('loadstart', () => {
            this.streamImg.classList.add('loading');
        });

        this.streamImg.addEventListener('load', () => {
            this.streamImg.classList.remove('loading');
            
            if (this.connectionIndicator && this.connectionText) {
                this.connectionIndicator.classList.replace('disconnected', 'connected');
                this.connectionText.textContent = 'Connected';
            }
            
            // Update resolution information
            if (this.streamResolution) {
                this.streamResolution.textContent = 
                    `${this.streamImg.naturalWidth}x${this.streamImg.naturalHeight}`;
            }
        });

        this.streamImg.addEventListener('error', () => {
            if (this.connectionIndicator && this.connectionText) {
                this.connectionIndicator.classList.replace('connected', 'disconnected');
                this.connectionText.textContent = 'Disconnected';
            }
        });
    }

    autoStartStream() {
        // Automatically start stream if URL is provided
        if (this.streamUrl && typeof startStream === 'function') {
            startStream();
        }
    }

    initializeZoneFilter() {
        // Zone filter activation
        setTimeout(() => {
            if (this.cameraId && typeof activateZoneFilter === 'function') {
                activateZoneFilter(this.cameraId);
            }
        }, 1000);
    }
}

// Initialize when script loads
new StreamUtilities();