from videostream.models import Zone
import logging
import traceback

logger = logging.getLogger(__name__)

def scale_zone_points(zone_points, video_width, video_height):
    """
    Scale zone points to match video dimensions.
    """
    scaled_polygon = []
    for p in zone_points:
        if isinstance(p, list) and len(p) == 2:
            scaled_polygon.append((p[0] * video_width, p[1] * video_height))
        elif isinstance(p, dict) and 'x' in p and 'y' in p:
            scaled_polygon.append((p['x'] * video_width, p['y'] * video_height))
    return scaled_polygon


def is_point_in_polygon(point, polygon):
    """
    Check if a point is inside a polygon using the ray-casting algorithm.

    Args:
        point: Tuple (x, y) representing the point to check.
        polygon: List of tuples [(x1, y1), (x2, y2), ...] representing the polygon.

    Returns:
        bool: True if the point is inside the polygon, False otherwise.
    """
    inside = False
    n = len(polygon)
    p1x, p1y = polygon[0]

    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if point[1] > min(p1y, p2y):
            if point[1] <= max(p1y, p2y):
                if point[0] <= max(p1x, p2x):
                    if p1y != p2y:
                        x_intersect = (point[1] - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or point[0] <= x_intersect:
                        inside = not inside
        p1x, p1y = p2x, p2y

    return inside

def process_zones(camera, submitted_zones):
    """Process submitted zones and update the database"""
    existing_zones = Zone.objects.filter(camera=camera)
    existing_zones_dict = {zone.name: zone for zone in existing_zones}

    zones_to_create, zones_to_delete = categorize_zones(submitted_zones, existing_zones_dict)

    delete_zones(zones_to_delete)
    save_zones(zones_to_create, camera)

    return {
        'status': 'success',
        'created': len(zones_to_create),
        'deleted': len(zones_to_delete)
    }

def categorize_zones(submitted_zones, existing_zones_dict):
    """Categorize zones into zones to create and zones to delete"""
    zones_to_create = []
    zones_to_delete = []
    submitted_zone_names = set()

    for zone_data in submitted_zones:
        zone_name = zone_data['name']
        submitted_zone_names.add(zone_name)

        if zone_name in existing_zones_dict:
            zones_to_delete.append(existing_zones_dict[zone_name])

        zones_to_create.append(zone_data)

    for zone_name, zone in existing_zones_dict.items():
        if zone_name not in submitted_zone_names:
            zones_to_delete.append(zone)

    return zones_to_create, zones_to_delete

def delete_zones(zones_to_delete):
    try:
        for zone in zones_to_delete:
            zone.delete()
    except Exception as e:
        logger.error(f"Error deleting zones: {str(e)}\n{traceback.format_exc()}")
        raise

    
def save_zones(zones_to_create, camera):
    """Save zones for a specific camera"""
    try:
        for zone_data in zones_to_create:
            Zone.objects.create(
                camera=camera,
                name=zone_data['name'],
                points=zone_data['points'],
                color=zone_data['color']
            )
    except Exception as e:
        logger.error(f"Error saving zones: {str(e)}\n{traceback.format_exc()}")
        raise