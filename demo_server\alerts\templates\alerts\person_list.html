{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="alert-listing-container">
<h2>Persons</h2>
<div class="d-flex gap-2 mb-3">
    <a href="{% url 'alerts:add_person' %}" class="btn btn-primary">Add New Person</a>
    <button type="button" id="delete-selected-btn" class="btn btn-danger" disabled>
        <i class="bi bi-trash"></i> Delete Selected
    </button>
</div>

<form id="person-form" method="post" action="{% url 'alerts:delete_selected_persons' %}">
    {% csrf_token %}
    <table class="table table-striped">
        <thead>
            <tr>
                <th>
                    <input type="checkbox" id="select-all-checkbox" class="form-check-input">
                </th>
                <th>Name</th>
                <th>Number of Photos</th>
                <th>Number of Alerts</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for person in persons %}
            <tr>
                <td>
                    <input type="checkbox" name="selected_persons" value="{{ person.id }}" class="form-check-input person-checkbox">
                </td>
                <td><a href="{% url 'alerts:person_detail' person.id %}">{{ person.name }}</a></td>
                <td>{{ person.photo_count }}</td>
                <td>{{ person.alarm_count }}</td>
                <td>
                    <a href="{% url 'alerts:person_detail' person.id %}" class="btn btn-sm btn-info">View Details</a>
                    <a href="{% url 'alerts:person_times' person.id %}" class="btn btn-sm btn-success ms-1">
                        <i class="bi bi-clock"></i> Times
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5">No persons added yet.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</form>

</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'alerts/js/person_list.js' %}"></script>
{% endblock %}