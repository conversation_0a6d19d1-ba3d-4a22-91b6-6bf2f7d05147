{% extends "base.html" %}
{% load static %}

{% block title %}Unknown Persons{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>Unknown Persons</h2>

    <div class="row">
        {% if page_obj %}
            {% for person in page_obj %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ person.name }}</h5>
                        <p class="card-text">First seen: {{ person.created_at|date:"d M Y, H:i" }}</p>
                        <p class="card-text">Last seen: {{ person.last_seen_date|date:"d M Y, H:i" }}</p>
                        <p class="card-text">First seen on camera: {{ person.first_seen_camera.name|default:"Unknown" }}</p>
                        <a href="{% url 'alerts:unknown_person_detail' person.id %}" class="btn btn-primary">View Details</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info">
                    No unknown persons found.
                </div>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">&laquo; first</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">previous</a>
                </li>
            {% endif %}

            {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }} <span class="sr-only">(current)</span></span>
                    </li>
                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}
