:root {
    --primary-color: #3a86ff;
    --dark-bg: #1a1a1a;
    --medium-bg: #2a2a2a;
    --light-bg: #333333;
    --text-light: #f8f9fa;
    --text-muted: #adb5bd;
    --danger: #e63946;
    --success: #2a9d8f;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-light);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--light-bg);
    margin-bottom: 20px;
}

h1 {
    font-size: 24px;
    font-weight: 500;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 992px) {
    .main-content {
        grid-template-columns: 3fr 1fr;
    }
}

.stream-container {
    background: var(--medium-bg);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    aspect-ratio: 16/9;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stream-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}


.controls-panel {
    background: var(--medium-bg);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.control-group {
    margin-bottom: 20px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group h3 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--text-muted);
    font-weight: 500;
}

.button-group {
    display: flex;
    gap: 10px;
}

button {
    background-color: var(--light-bg);
    color: var(--text-light);
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

button:hover {
    background-color: var(--primary-color);
}

button:active {
    transform: translateY(1px);
}

button.start-btn {
    background-color: var(--success);
}

button.stop-btn {
    background-color: var(--danger);
}

#frame-counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0,0,0,0.6);
    color: var(--text-light);
    padding: 5px 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-panel {
    background: var(--medium-bg);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--light-bg);
}

.status-item:last-child {
    border-bottom: none;
}

#status {
    margin-bottom: 10px;
    padding: 10px;
    background: var(--light-bg);
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
}

#debug-info {
    height: 100px;
    overflow: auto;
    padding: 10px;
    background: var(--light-bg);
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255,255,255,0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    display: none;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.stream-img.loading + .loading-spinner {
    display: block;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.connected {
    background-color: var(--success);
    box-shadow: 0 0 8px var(--success);
}

.status-indicator.disconnected {
    background-color: var(--danger);
    box-shadow: 0 0 8px var(--danger);
}

/* Zone Creator Specific Styles - Bu stiller sadece zone creator için */

/* Zone creator butonları için container */
.zone-creator-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
}

/* Zone creator butonları için özel stiller */
#create, #save-zones {
    background: linear-gradient(135deg, var(--light-bg) 0%, var(--medium-bg) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 120px;
}

#create::before, #save-zones::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

#create:hover, #save-zones:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a9eff 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(58, 134, 255, 0.3);
    border-color: var(--primary-color);
}

#create:hover::before, #save-zones:hover::before {
    left: 100%;
}

#create {
    background: linear-gradient(135deg, var(--success) 0%, #48bb9c 100%);
    border-color: var(--success);
}

#create:hover {
    background: linear-gradient(135deg, #48bb9c 0%, var(--success) 100%);
    box-shadow: 0 4px 12px rgba(42, 157, 143, 0.4);
}

#save-zones {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border-color: #f39c12;
}

#save-zones:hover {
    background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
}

/* Areas bölümü için özel stiller */
.areas {
    background: var(--medium-bg);
    border-radius: 10px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.areas h3 {
    margin-bottom: 12px;
    font-size: 16px;
    color: var(--text-light);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.areas h3::before {
    content: "☖";
    font-size: 18px;
}

#area-list {
    max-height: 200px;
    min-height: 60px;
    background: var(--dark-bg);
    border-radius: 8px;
    padding: 12px;
    border: 2px dashed rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--dark-bg);
}

/* Custom scrollbar styling */
#area-list::-webkit-scrollbar {
    width: 6px;
}

#area-list::-webkit-scrollbar-track {
    background: var(--dark-bg);
    border-radius: 3px;
}

#area-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

#area-list::-webkit-scrollbar-thumb:hover {
    background: #5a9eff;
}

#area-list:empty::before {
    content: "No zones created yet.";
    color: var(--text-muted);
    font-style: italic;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    text-align: center;
}

#area-list:not(:empty) {
    border-style: solid;
    border-color: rgba(58, 134, 255, 0.3);
}

/* Dinamik olarak oluşturulan polygon item'ları için */
.area-item {
    background: var(--light-bg);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.area-item:hover {
    background: rgba(58, 134, 255, 0.1);
    transform: translateX(4px);
}

.area-item:last-child {
    margin-bottom: 0;
}

.area-item.active {
    background: rgba(58, 134, 255, 0.2);
    border-left-color: #5a9eff;
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(58, 134, 255, 0.2);
}

.area-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-light);
}

.color-box {
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Switch Toggle Styles */
.switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.switch-label {
    color: var(--text-light);
    font-size: 14px;
    font-weight: 500;
    user-select: none;
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(14px);
}
