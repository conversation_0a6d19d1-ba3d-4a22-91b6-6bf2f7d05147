// Application initialization and utilities
class AppInitializer {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeAOS();
            this.setupWebSocket();
        });
    }

    initializeAOS() {
        // Initialize AOS animations
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                once: true,
                offset: 100
            });
        } else {
            console.warn('AOS library not found');
        }
    }

    setupWebSocket() {
        // WebSocket for real-time alerts (currently commented out)
        // Uncomment and modify as needed when WebSocket functionality is required
        /*
        const alertSocket = new WebSocket(
            'ws://' + window.location.host + '/ws/alerts/'
        );

        alertSocket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            if (data.type === 'alert') {
                if (typeof showToast === 'function') {
                    showToast(data.message, 'warning');
                }
            }
        };

        alertSocket.onclose = function(e) {
            console.error('Alert socket closed unexpectedly');
        };
        */
    }
}

// Initialize application
new AppInitializer();