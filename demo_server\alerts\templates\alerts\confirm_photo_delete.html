{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h4>Photo Deletion Confirmation</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 text-center">
                    <img src="{{ photo.photo.url }}" alt="{{ photo.person.name }}" class="img-fluid mb-3" style="max-height: 300px; object-fit: contain;">
                </div>
                <div class="col-md-8">
                    <h5>Are you sure you want to delete this photo?</h5>
                    <p><strong>Person:</strong> {{ photo.person.name }}</p>
                    <p><strong>Added Date:</strong> {{ photo.created_at|date:"d M Y, H:i" }}</p>
                    
                    {% if photo.is_primary %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> You are about to delete the main photo of this person. If there are other photos, one of the other photos will be automatically assigned as the main photo.
                    </div>
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex mt-4">
                            <button type="submit" class="btn btn-danger me-2">Delete photo</button>
                            <a href="{% url 'alerts:person_detail' person_id=photo.person.id %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}