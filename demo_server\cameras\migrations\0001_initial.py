# Generated by Django 5.1.4 on 2025-05-29 09:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Camera',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('rtsp_url', models.CharField(max_length=255)),
                ('camera_type', models.CharField(max_length=50)),
                ('is_streaming', models.BooleanField(default=False)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('frame_drop_rate', models.FloatField(default=0.0)),
                ('latency', models.FloatField(default=0.0)),
                ('fps', models.FloatField(default=0.0)),
                ('recognizer', models.CharField(choices=[('arcface', 'ArcFaceRecognizer'), ('facenet', 'FaceNetRecognizer')], default='arcface', max_length=10)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cameras', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Flat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='flats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Guest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('photo', models.ImageField(upload_to='guest_photos/')),
                ('entry_date_time', models.DateTimeField(blank=True, null=True)),
                ('exit_date_time', models.DateTimeField(blank=True, null=True)),
                ('recurring', models.BooleanField(default=False)),
                ('recurring_days', models.CharField(blank=True, help_text='Days of the week for recurring visits (e.g., Mon,Wed,Fri)', max_length=100, null=True)),
                ('access_duration', models.DurationField(blank=True, help_text='Maximum duration for guest access', null=True)),
                ('flat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guests', to='cameras.flat')),
            ],
        ),
    ]
