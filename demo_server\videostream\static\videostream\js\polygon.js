class Polygon {
    constructor(color) {
      this.points = [];
      this.color = color;
      this.complete = false;
      this.element = null;
      this.r = 6;
      this.lineWidth = 2;
      this.opacity = 80; // 0-255
      this.pointColor = 'blue';
      this.lineColor = 'white';
      this.name = ''; // Initialize as empty, will be set when created
    }
  
    addPoint(pos) {
      this.points.push(pos);
    }
  
    removePointAt(index) {
      if (index !== -1) this.points.splice(index, 1);
    }
  
    movePoint(index, pos) {
      if (index !== -1) this.points[index] = pos;
    }
  
    draw(ctx) {
      if (this.points.length === 0) return;
  
      ctx.beginPath();
      ctx.moveTo(this.points[0].x, this.points[0].y);
      for (let i = 1; i < this.points.length; i++) {
        ctx.lineTo(this.points[i].x, this.points[i].y);
      }
      if (this.complete) ctx.closePath();
  
      ctx.fillStyle = this.color + `${this.opacity}`;
      ctx.fill();
      ctx.strokeStyle = this.color;
      ctx.lineWidth = this.lineWidth;
      ctx.stroke();
  
      for (const pt of this.points) {
        this.drawPoint(ctx, pt);
      }
    }
  
    drawPoint(ctx, p) {
      ctx.beginPath();
      ctx.arc(p.x, p.y, this.r, 0, 2 * Math.PI);
      ctx.fillStyle = this.pointColor;
      ctx.fill();
      ctx.strokeStyle = this.lineColor;
      ctx.lineWidth = this.lineWidth;
      ctx.stroke();
    }
  

    clear(ctx) {
      ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    }
  }

window.Polygon = Polygon;
