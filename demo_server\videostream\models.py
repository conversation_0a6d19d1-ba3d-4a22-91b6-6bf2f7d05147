from django.db import models
from cameras.models import Camera

class Zone(models.Model):
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='zones')
    points = models.JSONField()  # Store polygon points as JSON
    color = models.CharField(max_length=7)  # Store color as a hex code (e.g., #FF0000)
    name = models.CharField(max_length=255)  # Add name property

    def __str__(self):
        return f"Zone '{self.name}' for Camera {self.camera.id}"
