class CanvasInteractionHandler {
    constructor(canvas, getCurrentPolygon, onDrawRequest) {
      this.canvas = canvas;
      this.getCurrentPolygon = getCurrentPolygon;
      this.onDrawRequest = onDrawRequest;
  
      this.draggingIndex = null;
      this.isDragging = false;
  
      this.canvas.addEventListener("mousedown", this.handleMouseDown.bind(this));
      this.canvas.addEventListener("mousemove", this.handleMouseMove.bind(this));
      this.canvas.addEventListener("mouseup", this.handleMouseUp.bind(this));
      this.canvas.addEventListener("contextmenu", this.handleRightClick.bind(this));
    }
  
    getMousePos(evt) {
      const rect = this.canvas.getBoundingClientRect();
      return {
        x: evt.clientX - rect.left,
        y: evt.clientY - rect.top
      };
    }
  
    getPointAtPosition(pos, polygon) {
      return polygon.points.findIndex(p => {
        const dx = p.x - pos.x;
        const dy = p.y - pos.y;
        return Math.sqrt(dx * dx + dy * dy) < 8;
      });
    }
  
    handleMouseDown(e) {
      const pos = this.getMousePos(e);
      const currentPolygon = this.getCurrentPolygon();
      if (!currentPolygon || e.button !== 0) return;
  
      const index = this.getPointAtPosition(pos, currentPolygon);
      if (index !== -1) {
        this.draggingIndex = index;
        this.isDragging = true;
      } else {
        currentPolygon.addPoint(pos);
        this.onDrawRequest();
      }
    }
  
    handleMouseMove(e) {
      if (!this.isDragging || this.draggingIndex === null) return;
      const pos = this.getMousePos(e);
      const currentPolygon = this.getCurrentPolygon();
      if (!currentPolygon) return;
  
      currentPolygon.movePoint(this.draggingIndex, pos);
      this.onDrawRequest();
    }
  
    handleMouseUp() {
      this.isDragging = false;
      this.draggingIndex = null;
    }
  
    handleRightClick(e) {
      e.preventDefault();
      const pos = this.getMousePos(e);
      const currentPolygon = this.getCurrentPolygon();
      if (!currentPolygon) return;
  
      const index = this.getPointAtPosition(pos, currentPolygon);
      if (index !== -1) {
        currentPolygon.removePointAt(index);
        this.onDrawRequest();
      }
    }
  }
  
window.CanvasInteractionHandler = CanvasInteractionHandler;
  