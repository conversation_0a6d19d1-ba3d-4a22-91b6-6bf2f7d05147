from django.db import models
from django.conf import settings

# Create your models here.

class Camera(models.Model):
    CAMERA_TYPE_CHOICES = [
        ('ENTRY', 'Entry Camera'),
        ('EXIT', 'Exit Camera'),
        # BOTH seçeneği kald<PERSON> - her kamera tek amaçlı olmalı
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='cameras')
    venue = models.ForeignKey('alerts.Venue', on_delete=models.SET_NULL, related_name='cameras', 
                             help_text='Bu kameranın ait olduğu mekan', null=True, blank=True)
    name = models.CharField(max_length=100)
    rtsp_url = models.CharField(max_length=255)
    camera_type = models.CharField(
        max_length=10,
        choices=CAMERA_TYPE_CHOICES,
        default='ENTRY',
        help_text='Camera type for visit tracking'
    )
    is_streaming = models.BooleanField(default=False)  # New field to track streaming status
    last_seen = models.DateTimeField(null=True, blank=True)
    frame_drop_rate = models.FloatField(default=0.0)
    latency = models.FloatField(default=0.0)
    fps = models.FloatField(default=0.0)

    def is_healthy(self):
        # Basic health check: is the camera streaming?
        return self.is_streaming

    def __str__(self):
        venue_name = self.venue.name if self.venue else "Venue Atanmamış"
        return f"{self.name} ({self.get_camera_type_display()}) - {venue_name}"

    # Add any other fields or methods as needed