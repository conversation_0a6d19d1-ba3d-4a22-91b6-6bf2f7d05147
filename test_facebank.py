import torch
import numpy as np
import os

# Facebank dosyalarını yükle
facebank_path = r'demo_server\media\alert_photos\facebank.pth'
names_path = r'demo_server\media\alert_photos\names.npy'

print("=== FACEBANK.PTH ===")
try:
    facebank = torch.load(facebank_path, map_location='cpu')
    names = np.load(names_path, allow_pickle=True)
    
    print(f"Tensor shape: {facebank.shape}")
    print(f"Names count: {len(names)}")
    print("Names:", names)
except Exception as e:
    print(f"Error: {e}")

print("\n=== UNKNOWN_FACEBANK.PTH ===")
unknown_facebank_path = r'demo_server\media\alert_photos\unknowns\unknown_facebank.pth'
unknown_names_path = r'demo_server\media\alert_photos\unknowns\unknown_names.npy'

try:
    unknown_facebank = torch.load(unknown_facebank_path, map_location='cpu')
    unknown_names = np.load(unknown_names_path, allow_pickle=True)
    
    if unknown_facebank is not None:
        print(f"Tensor shape: {unknown_facebank.shape}")
    else:
        print("Tensor: None")
    print(f"Names count: {len(unknown_names)}")
    print("Names:", unknown_names)
except Exception as e:
    print(f"Error: {e}")
