"""
Django management command to delete admin (superuser) accounts
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserProfile, UserRoleHistory


class Command(BaseCommand):
    help = 'Delete admin (superuser) user - USE WITH CAUTION!'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Admin username to delete',
            required=True
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion (required for safety)'
        )

    def handle(self, *args, **options):
        username = options['username']
        confirm = options['confirm']

        if not confirm:
            self.stdout.write(
                self.style.ERROR(
                    'Deletion not confirmed. Use --confirm flag to proceed.\n'
                    'Example: python manage.py delete_admin --username admin --confirm'
                )
            )
            return

        try:
            # Find the user
            user = User.objects.get(username=username)

            # Security check - only allow deletion of superusers
            if not user.is_superuser:
                self.stdout.write(
                    self.style.ERROR(
                        f'User {username} is not a superuser. '
                        'This command only deletes admin/superuser accounts.'
                    )
                )
                return

            # Warning message
            self.stdout.write(
                self.style.WARNING(
                    f'WARNING: About to delete superuser account: {username}\n'
                    f'Email: {user.email}\n'
                    f'Date joined: {user.date_joined}\n'
                    f'Last login: {user.last_login}'
                )
            )

            # Final confirmation
            response = input('Type "DELETE" to confirm deletion: ')
            if response != 'DELETE':
                self.stdout.write(
                    self.style.ERROR('Deletion cancelled.')
                )
                return

            # Delete related UserProfile safely
            deleted, _ = UserProfile.objects.filter(user=user).delete()
            if deleted:
                self.stdout.write('User profile deleted.')

            # Log deletion into UserRoleHistory (audit trail)
            UserRoleHistory.objects.create(
                user=user,
                role_name='superuser',
                action='deleted',
                assigned_by=None,  # because it's deleted via CLI
                reason='Deleted via management command delete_admin'
            )

            # Delete the user
            user.delete()

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully deleted admin user: {username}\n'
                    f'IMPORTANT: Make sure you have another admin account or create a new one'
                )
            )

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User {username} does not exist')
            )
        except Exception as e:
            self.stderr.write(f"Error deleting admin user: {str(e)}")
