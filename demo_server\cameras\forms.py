from django import forms
from .models import Camera
from alerts.models import Venue

class CameraForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Venue seçimini kullanıcının venue'larıyla sınırla
        if user:
            self.fields['venue'].queryset = Venue.objects.filter(user=user, is_active=True)
            self.fields['venue'].empty_label = "Mekan seçiniz"
        
    class Meta:
        model = Camera
        fields = ['name', 'venue', 'camera_type', 'rtsp_url']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'örn: Ana Giriş Kamerası'}),
            'venue': forms.Select(attrs={'class': 'form-control'}),
            'camera_type': forms.Select(attrs={'class': 'form-control'}),
            'rtsp_url': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'rtsp://username:password@ip:port/stream'}),
        }
        labels = {
            'name': 'Kamera Adı',
            'venue': 'Mekan',
            'camera_type': 'Kamera Tipi',
            'rtsp_url': 'RTSP URL',
        }
        help_texts = {
            'venue': 'Kameranın hangi mekana ait olduğunu seçin',
            'camera_type': 'Entry: Mekana giriş için, Exit: Mekan çıkışı için',
            'rtsp_url': 'Kameranın RTSP stream adresi',
        }
