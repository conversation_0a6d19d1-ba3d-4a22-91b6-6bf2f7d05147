{% extends 'stream_viewer.html' %}
{% load static %}
{% block title %}Zone Creator{% endblock %}
{% block header_title %}<i class="fas fa-draw-polygon"></i> Zone Creator{% endblock %}
{% block canvas_element %}
    <canvas id="draw-canvas" class="draw-canvas"></canvas>
{% endblock %}
{% block zone_visibility_js %}{% endblock %}
{% block stream_start_controls %}
<div class="control-group">
    <h3>Stream Controls</h3>
    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
    <div class="button-group">
        <button class="start-btn" onclick="startStream()">
            <i class="fas fa-play"></i> Start
        </button>
        <button class="stop-btn" onclick="stopStream()">
            <i class="fas fa-stop"></i> Stop
        </button>
    </div>
</div>
{% endblock %}
{% block zone_creator %}
<div class="control-group">
    {% block zone_creator_buttons %}
    <div class="zone-creator-buttons">
        <button id="create"><i class="fas fa-plus"></i> Create Polygon</button>
        <button id="finish-edit" style="display: none;"><i class="fas fa-check"></i> Finish Edit</button>
        <button id="save-zones"><i class="fas fa-save"></i> Save Zones</button>
    </div>
    {% endblock %}
    <div class="areas">
        <h3>Areas</h3>
        <div id="area-list"></div>
    </div>
</div>
{% endblock %}

{% block zone_creator_js %}
<script src="{% static 'videostream/js/sidebar_manager.js' %}"></script>
<script src="{% static 'videostream/js/canvas_interaction_handler.js' %}"></script>
<script>
    // Set save zones URL for the JavaScript module
    window.saveZonesUrl = '{% url "videostream:save_zones" %}';
</script>
<script src="{% static 'videostream/js/zone_creator.js' %}"></script>
{% endblock %}