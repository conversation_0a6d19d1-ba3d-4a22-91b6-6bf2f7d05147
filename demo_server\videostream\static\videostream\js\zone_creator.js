// Zone creator functionality for creating and managing zones
class ZoneCreator {
    constructor() {
        this.polygons = [];
        this.currentPolygon = null;
        this.polygonCount = 0;
        this.canvas = null;
        this.video = null;
        this.cameraId = null;
        this.canvasManager = null;
        this.sidebar = null;
        this.interactionHandler = null;
        
        this.init();
    }

    init() {
        window.addEventListener('load', () => {
            this.initializeElements();
            this.setupInitialConfiguration();
        });
    }

    initializeElements() {
        this.canvas = document.getElementById("draw-canvas");
        this.video = document.getElementById("stream");
        this.cameraId = document.getElementById("camera-id")?.value;

        if (!this.canvas || !this.video) {
            console.error("Required elements not found");
            return false;
        }

        return true;
    }

    setupInitialConfiguration() {
        if (!this.initializeElements()) return;

        // Activate zone filter
        setTimeout(() => {
            if (typeof activateZoneFilter === 'function' && this.cameraId) {
                activateZoneFilter(this.cameraId);
            }
        }, 1000);

        this.setupCanvasManager();
        this.setupSidebar();
        this.setupEventListeners();
        this.setupUpdateInterval();
        
        // Initialize after delay
        setTimeout(() => {
            this.initializeZoneCreator();
        }, 3000);
    }

    setupCanvasManager() {
        if (typeof CanvasManager !== 'undefined') {
            this.canvasManager = new CanvasManager(this.canvas);
        } else {
            console.error("CanvasManager not found");
        }
    }

    setupSidebar() {
        const areaList = document.getElementById("area-list");
        if (!areaList || typeof SidebarManager === 'undefined') {
            console.error("SidebarManager or area-list not found");
            return;
        }

        this.sidebar = new SidebarManager(
            areaList,
            (polygon) => this.handlePolygonEdit(polygon),
            (polygon, div) => this.handlePolygonDelete(polygon, div)
        );
    }

    handlePolygonEdit(polygon) {
        // Set current polygon for editing
        this.currentPolygon = polygon;
        this.currentPolygon.complete = false; // Make it editable
        this.canvasManager.setPolygons(this.polygons, this.currentPolygon);
        this.canvasManager.draw();
        
        // Highlight the active polygon in sidebar
        this.sidebar.highlightActive(this.polygons, this.currentPolygon);
        
        // Show finish edit button
        this.toggleEditButtons(true);
        
        console.log("Edit mode activated for:", polygon.name);
    }

    handlePolygonDelete(polygon, div) {
        this.polygons = this.polygons.filter(p => p !== polygon);
        if (this.currentPolygon === polygon) {
            this.currentPolygon = null;
            this.toggleEditButtons(false);
        }
        this.sidebar.removePolygonLabel(div);
        this.canvasManager.setPolygons(this.polygons, this.currentPolygon);
        this.canvasManager.draw();
    }

    toggleEditButtons(editMode) {
        const finishEditBtn = document.getElementById("finish-edit");
        const createBtn = document.getElementById("create");
        
        if (finishEditBtn && createBtn) {
            finishEditBtn.style.display = editMode ? "inline-block" : "none";
            createBtn.style.display = editMode ? "none" : "inline-block";
        }
    }

    setupEventListeners() {
        // Video event listeners
        this.video.addEventListener('load', () => this.updateCanvasPosition());
        this.video.addEventListener('loadeddata', () => this.updateCanvasPosition());
        this.video.addEventListener('resize', () => this.updateCanvasPosition());

        // Button event listeners
        this.setupButtonEventListeners();
        
        // Keyboard shortcuts
        this.setupKeyboardShortcuts();
    }

    setupButtonEventListeners() {
        const createBtn = document.getElementById("create");
        const finishEditBtn = document.getElementById("finish-edit");
        const saveZonesBtn = document.getElementById("save-zones");

        if (createBtn) {
            createBtn.addEventListener("click", () => this.createNewPolygon());
        }

        if (finishEditBtn) {
            finishEditBtn.addEventListener("click", () => this.finishEdit());
        }

        if (saveZonesBtn) {
            saveZonesBtn.addEventListener("click", () => this.saveZones());
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentPolygon) {
                this.finishEdit();
            }
        });
    }

    setupUpdateInterval() {
        if (this.video.complete) {
            this.updateCanvasPosition();
        }

        setInterval(() => this.updateCanvasPosition(), 1000);
        setTimeout(() => this.updateCanvasPosition(), 500);
    }

    updateCanvasPosition() {
        if (!this.canvasManager || !this.video) return;

        if (this.video.complete && this.video.naturalWidth !== 0) {
            this.canvasManager.resizeToMatch(this.video);
            this.canvasManager.draw();
        }
    }

    async fetchZones() {
        if (!this.cameraId) {
            console.log("No camera ID available for fetching zones");
            return;
        }

        try {
            const response = await fetch(`/videostream/get_zones/?camera_id=${this.cameraId}`);
            const result = await response.json();
            
            if (response.ok) {
                this.processExistingZones(result.zones || []);
            } else {
                console.error(`Error fetching zones: ${result.error}`);
            }
        } catch (error) {
            console.error("Error fetching zones:", error);
        }
    }

    processExistingZones(zones) {
        const videoWidth = this.video.clientWidth;
        const videoHeight = this.video.clientHeight;

        this.polygons = zones.map(zone => {
            const polygon = new Polygon(zone.color);
            polygon.name = zone.name;
            polygon.points = zone.points.map(point => ({
                x: point.x * videoWidth,
                y: point.y * videoHeight
            }));
            polygon.complete = true;
            this.sidebar.addPolygonLabel(zone.name, zone.color, polygon);
            return polygon;
        });

        this.canvasManager.setPolygons(this.polygons);
        this.canvasManager.draw();
    }

    setupInteractionHandler() {
        if (typeof CanvasInteractionHandler !== 'undefined') {
            this.interactionHandler = new CanvasInteractionHandler(
                this.canvas,
                () => this.currentPolygon,
                () => this.canvasManager.draw()
            );
        } else {
            console.error("CanvasInteractionHandler not found");
        }
    }

    createNewPolygon() {
        console.log("Creating new polygon");

        if (this.currentPolygon && this.currentPolygon.points.length > 2) {
            this.currentPolygon.complete = true;
        }

        const color = "#" + Math.floor(Math.random() * 16777215).toString(16);
        this.polygonCount++;

        this.currentPolygon = new Polygon(color);
        this.currentPolygon.name = `Polygon ${this.polygonCount}`;
        this.polygons.push(this.currentPolygon);

        this.sidebar.addPolygonLabel(this.currentPolygon.name, color, this.currentPolygon);
        this.canvasManager.setPolygons(this.polygons, this.currentPolygon);
        this.canvasManager.draw();
        
        // Highlight the active polygon in sidebar
        this.sidebar.highlightActive(this.polygons, this.currentPolygon);
        
        // Show finish edit button
        this.toggleEditButtons(true);
    }

    finishEdit() {
        console.log("Finishing edit mode");

        if (this.currentPolygon && this.currentPolygon.points.length > 2) {
            this.currentPolygon.complete = true;
            this.canvasManager.draw();
        }
        
        // Clear current polygon selection
        this.currentPolygon = null;
        this.canvasManager.setPolygons(this.polygons, this.currentPolygon);
        
        // Reset buttons
        this.toggleEditButtons(false);
        
        // Clear sidebar highlighting
        this.sidebar.highlightActive(this.polygons, null);
    }

    async saveZones() {
        if (!this.cameraId) {
            alert("No camera ID found");
            return;
        }

        const videoWidth = this.video.videoWidth || this.video.clientWidth;
        const videoHeight = this.video.videoHeight || this.video.clientHeight;

        const zones = this.polygons.map(polygon => {
            const normalizedPoints = polygon.points.map(point => ({
                x: point.x / videoWidth,
                y: point.y / videoHeight
            }));
            return {
                name: polygon.name || `Polygon ${polygon.id}`,
                points: normalizedPoints,
                color: polygon.color
            };
        });

        try {
            // Get CSRF token from meta tag or form
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                             document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            const response = await fetch(window.saveZonesUrl || '/videostream/save_zones/', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": csrfToken
                },
                body: JSON.stringify({ camera_id: this.cameraId, zones })
            });

            const result = await response.json();
            if (response.ok) {
                alert("Zones saved successfully!");
            } else {
                alert(`Error saving zones: ${result.error}`);
            }
        } catch (error) {
            console.error("Error saving zones:", error);
            alert("An error occurred while saving zones.");
        }
    }

    initializeZoneCreator() {
        if (this.video.complete) {
            this.setupInteractionHandler();
            this.fetchZones();
        } else {
            this.video.onload = () => {
                this.setupInteractionHandler();
                this.fetchZones();
            };
        }
    }
}

// Initialize zone creator when script loads
new ZoneCreator();