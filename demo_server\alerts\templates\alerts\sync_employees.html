{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'alerts/css/sync_employees.css' %}">
{% endblock %}

{% block extra_js %}
<script src="{% static 'alerts/js/sync_employees.js' %}"></script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    {% csrf_token %}
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-10">
            <h2><i class="bi bi-arrow-repeat"></i> Employee Synchronization</h2>
            <p class="text-muted">Sync employees from company API to local database</p>
        </div>
        <div class="col-md-2 text-end">
            <a href="{% url 'alerts:person_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Persons
            </a>
        </div>
    </div>

    <!-- API Status Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-info-circle"></i> API Status</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="status-item">
                        <div class="status-label">API Status</div>
                        <div class="status-value {% if api_config.api_enabled %}text-success{% else %}text-danger{% endif %}">
                            {% if api_config.api_enabled %}
                                <i class="bi bi-check-circle"></i> Enabled
                            {% else %}
                                <i class="bi bi-x-circle"></i> Disabled
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-item">
                        <div class="status-label">Endpoint</div>
                        <div class="status-value">{{ api_config.endpoint }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-item">
                        <div class="status-label">Cache Status</div>
                        <div class="status-value {% if api_config.has_cached_data %}text-success{% else %}text-warning{% endif %}">
                            {% if api_config.has_cached_data %}
                                <i class="bi bi-check-circle"></i> Cached ({{ api_config.cached_employee_count }})
                            {% else %}
                                <i class="bi bi-exclamation-triangle"></i> No Cache
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-item">
                        <div class="status-label">Local Employees</div>
                        <div class="status-value text-info">
                            <i class="bi bi-people"></i> {{ existing_employees }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Control Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-gear"></i> Synchronization Control</h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6>Sync Process</h6>
                    <p class="text-muted mb-2">
                        This will fetch all employees from the company API and add any missing employees to the local database.
                        Existing employees will be updated if their information has changed.
                    </p>
                    <div class="sync-progress" style="display: none;">
                        <div class="progress mb-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="progress-status">Preparing synchronization...</div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button id="sync-btn" class="btn btn-primary btn-lg" {% if not api_config.api_enabled %}disabled{% endif %}>
                        <i class="bi bi-arrow-repeat"></i> Start Sync
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Card -->
    <div id="sync-results" class="card" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-list-check"></i> Sync Results</h5>
        </div>
        <div class="card-body">
            <div class="row" id="stats-row">
                <div class="col-md-2">
                    <div class="stat-card api-employees">
                        <div class="stat-number" id="api-employees-count">0</div>
                        <div class="stat-label">API Employees</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card existing-employees">
                        <div class="stat-number" id="existing-employees-count">0</div>
                        <div class="stat-label">Existing Employees</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card new-employees">
                        <div class="stat-number" id="new-employees-count">0</div>
                        <div class="stat-label">New Added</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card updated-employees">
                        <div class="stat-number" id="updated-employees-count">0</div>
                        <div class="stat-label">Updated</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card deleted-employees">
                        <div class="stat-number" id="deleted-employees-count">0</div>
                        <div class="stat-label">Deleted</div>
                    </div>
                </div>
            </div>

            <!-- New Employees List -->
            <div id="new-employees-list" style="display: none;">
                <h6 class="mt-4">New Employees Added:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Employee ID</th>
                                <th>Name</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="new-employees-tbody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Updated Employees List -->
            <div id="updated-employees-list" style="display: none;">
                <h6 class="mt-4">Updated Employees:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Employee ID</th>
                                <th>Name</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="updated-employees-tbody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Deleted Employees List -->
            <div id="deleted-employees-list" style="display: none;">
                <h6 class="mt-4">Deleted Employees:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Employee ID</th>
                                <th>Name</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="deleted-employees-tbody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Card -->
    <div id="sync-error" class="card border-danger" style="display: none;">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Sync Error</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-danger" id="error-message">
                <!-- Error message will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}