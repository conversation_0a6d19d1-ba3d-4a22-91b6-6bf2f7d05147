document.addEventListener('DOMContentLoaded', function() {
    const syncBtn = document.getElementById('sync-btn');
    const progressContainer = document.querySelector('.sync-progress');
    const progressBar = document.querySelector('.progress-bar');
    const progressStatus = document.querySelector('.progress-status');
    const resultsCard = document.getElementById('sync-results');
    const errorCard = document.getElementById('sync-error');

    syncBtn.addEventListener('click', function() {
        startSync();
    });

    function startSync() {
        // Disable button and show progress
        syncBtn.disabled = true;
        syncBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Syncing...';
        progressContainer.style.display = 'block';
        progressBar.style.width = '20%';
        progressStatus.textContent = 'Connecting to API...';
        
        // Hide previous results/errors
        resultsCard.style.display = 'none';
        errorCard.style.display = 'none';

        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('input[name="csrfmiddlewaretoken"]')?.value ||
                         getCookie('csrftoken');

        console.log('Starting AJAX request to sync employees...');
        console.log('CSRF Token:', csrfToken);

        // AJAX call to sync endpoint
        fetch('/alert/sync-employees/ajax/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({})
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response.json();
        })
        .then(data => {
            progressBar.style.width = '100%';
            progressStatus.textContent = 'Sync completed!';
            
            setTimeout(() => {
                progressContainer.style.display = 'none';
                
                if (data.success) {
                    showResults(data);
                } else {
                    showError(data.error || 'Unknown error occurred');
                }
                
                // Re-enable button
                syncBtn.disabled = false;
                syncBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i> Start Sync';
            }, 1000);
        })
        .catch(error => {
            progressContainer.style.display = 'none';
            showError('Network error: ' + error.message);
            
            // Re-enable button
            syncBtn.disabled = false;
            syncBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i> Start Sync';
        });
    }

    function showResults(data) {
        // Update statistics
        document.getElementById('api-employees-count').textContent = data.stats.api_employees;
        document.getElementById('existing-employees-count').textContent = data.stats.existing_employees;
        document.getElementById('new-employees-count').textContent = data.stats.new_employees;
        document.getElementById('updated-employees-count').textContent = data.stats.updated_employees;
        document.getElementById('deleted-employees-count').textContent = data.stats.deleted_employees || 0;

        // Show new employees
        if (data.new_employees && data.new_employees.length > 0) {
            const tbody = document.getElementById('new-employees-tbody');
            tbody.innerHTML = '';
            
            data.new_employees.forEach(emp => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><code>${emp.employee_id}</code></td>
                    <td>${emp.name}</td>
                    <td><span class="badge bg-success">Added</span></td>
                `;
            });
            
            document.getElementById('new-employees-list').style.display = 'block';
        }

        // Show updated employees
        if (data.updated_employees && data.updated_employees.length > 0) {
            const tbody = document.getElementById('updated-employees-tbody');
            tbody.innerHTML = '';
            
            data.updated_employees.forEach(emp => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><code>${emp.employee_id}</code></td>
                    <td>${emp.name}</td>
                    <td><span class="badge bg-info">Updated</span></td>
                `;
            });
            
            document.getElementById('updated-employees-list').style.display = 'block';
        }

        // Show deleted employees
        if (data.deleted_employees && data.deleted_employees.length > 0) {
            const tbody = document.getElementById('deleted-employees-tbody');
            tbody.innerHTML = '';
            
            data.deleted_employees.forEach(emp => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><code>${emp.employee_id}</code></td>
                    <td>${emp.name}</td>
                    <td><span class="badge bg-danger">Deleted</span></td>
                `;
            });
            
            document.getElementById('deleted-employees-list').style.display = 'block';
        }

        resultsCard.style.display = 'block';
    }

    function showError(message) {
        document.getElementById('error-message').innerHTML = '<strong>Error:</strong> ' + message;
        errorCard.style.display = 'block';
    }

    // Helper function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});