from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON><PERSON>, AlertPhoto, Alarm

class AlertPhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = AlertPhoto
        fields = ['id', 'photo', 'created_at']

class AlertPersonSerializer(serializers.ModelSerializer):
    photos = AlertPhotoSerializer(many=True, read_only=True)

    class Meta:
        model = AlertPerson
        fields = ['id', 'name', 'photos', 'created_at', 'updated_at']

class AlarmSerializer(serializers.ModelSerializer):
    person_name = serializers.CharField(source='person.name', read_only=True)
    camera_name = serializers.CharField(source='camera.name', read_only=True)

    class Meta:
        model = Alarm
        fields = ['id', 'person_name', 'camera_name', 'date', 'video_snapshot']